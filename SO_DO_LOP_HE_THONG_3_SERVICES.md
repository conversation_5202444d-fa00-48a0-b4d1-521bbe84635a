# SƠ ĐỒ LỚP HỆ THỐNG 3 SERVICES: VEHICLE, TRAINREGION, MODELSTATS

## 1. TỔNG QUAN KIẾN TRÚC

Hệ thống được thiết kế theo mô hình **Microservices Architecture** với 3 services độc lập:

### **🚗 Vehicle Service (Port 3002)**
- **Công nghệ**: Node.js + Express + Sequelize ORM
- **Database**: vehicle_db (MySQL)
- **Chức năng**: Quản lý thông tin đăng ký xe

### **🤖 TrainRegion Service (Port 3003)**
- **Công nghệ**: Python + Flask + SQLAlchemy ORM
- **Database**: trainregion_db (MySQL)
- **Chức năng**: Training models AI cho nhận diện biển số

### **📊 ModelStats Service (Port 3007)**
- **Công nghệ**: Node.js + Express + Sequelize ORM
- **Database**: modelstats_db (MySQL)
- **Ch<PERSON><PERSON> năng**: Thống kê và phân tích hiệu suất models

## 2. CHI TIẾT TỪNG SERVICE

### 2.1 VEHICLE SERVICE

#### **Models (Data Layer)**
```javascript
class Vehicle {
    // Thông tin cơ bản xe
    +id, licensePlate, ownerName, ownerID
    +vehicleType, brand, model, color
    +registrationDate, expiryDate, status
    +createdAt, updatedAt
}
```

#### **Controllers (Business Logic Layer)**
```javascript
class VehicleController {
    +getAllVehicles()           // Lấy danh sách tất cả xe
    +getVehicleById(id)         // Lấy xe theo ID
    +getVehicleByLicensePlate() // Lấy xe theo biển số
    +createVehicle(data)        // Tạo xe mới
    +updateVehicle(id, data)    // Cập nhật thông tin xe
    +deleteVehicle(id)          // Xóa xe
}
```

#### **Routes (API Layer)**
- `GET /api/vehicles` - Danh sách xe
- `GET /api/vehicles/:id` - Chi tiết xe
- `GET /api/vehicles/plate/:licensePlate` - Tìm theo biển số
- `POST /api/vehicles` - Tạo xe mới
- `PUT /api/vehicles/:id` - Cập nhật xe
- `DELETE /api/vehicles/:id` - Xóa xe

### 2.2 TRAINREGION SERVICE

#### **Models (Data Layer)**
```python
class Model:
    # Thông tin model AI đã train
    +id, name, type, version, epochs, batch_size
    +training_time, map50, map95, model_path
    +is_approved, approval_notes, dataset_id

class TrainingJob:
    # Thông tin job training
    +id, status, progress, current_epoch
    +can_stop, stop_requested, stopped_at
    +best_map50, best_map95, result_path

class Dataset:
    # Thông tin dataset để training
    +id, name, upload_path, image_count
    +has_labels, labeling_method, status
```

#### **Controllers (Business Logic Layer)**
```python
class ModelController:
    +get_all_models()       // Lấy danh sách models
    +approve_model(id)      // Phê duyệt model
    +reject_model(id)       // Từ chối model

class TrainingController:
    +start_training()       // Bắt đầu training
    +stop_training(job_id)  // Dừng training
    +get_training_progress() // Lấy tiến độ training

class DatasetController:
    +upload_images_smart()  // Upload ảnh với auto-labeling
    +get_datasets()         // Lấy danh sách datasets
```

#### **Routes (API Layer)**
- `POST /api/training/start` - Bắt đầu training
- `POST /api/training/stop/:id` - Dừng training
- `GET /api/training/progress/:id` - Tiến độ training
- `POST /api/dataset/upload-images` - Upload dataset
- `POST /api/model/approve/:id` - Phê duyệt model

### 2.3 MODELSTATS SERVICE

#### **Models (Data Layer)**
```javascript
class TrainingHistory {
    // Lịch sử training từ TrainRegion
    +id, model_id, model_name, model_type
    +dataset_name, epochs, batch_size
    +training_time, map50, map95, status
    +is_approved, created_at
}
```

#### **Database Views**
```sql
-- Thống kê tổng quan
v_model_stats_summary: 
    total_models, completed_models, avg_map50, success_rate

-- Top models hiệu suất cao
v_top_models: 
    model_name, composite_score, map50, map95

-- Thống kê theo loại model
v_model_type_stats: 
    model_type, avg_map50, best_map50, avg_training_time
```

#### **Controllers (Business Logic Layer)**
```javascript
class ModelStatsController {
    +getModelStatsSummary()     // Thống kê tổng quan
    +getTopModels()             // Top models hiệu suất cao
    +getModelTypeStats()        // Thống kê theo loại model
    +getAllTrainingHistory()    // Lịch sử training với filter
    +syncFromTrainRegion()      // Đồng bộ dữ liệu từ TrainRegion
}
```

#### **Routes (API Layer)**
- `GET /api/stats/summary` - Thống kê tổng quan
- `GET /api/stats/top-models` - Top models
- `GET /api/stats/by-type` - Thống kê theo loại
- `GET /api/stats/history` - Lịch sử training
- `POST /api/stats/sync` - Đồng bộ dữ liệu

## 3. MỐI QUAN HỆ GIỮA CÁC SERVICES

### 3.1 Quan hệ nội bộ từng service

#### **Vehicle Service:**
```
VehicleRoutes → VehicleController → Vehicle Model
```

#### **TrainRegion Service:**
```
Routes → Controllers → Models
ModelRoutes → ModelController → Model
TrainingRoutes → TrainingController → TrainingJob → Model
DatasetRoutes → DatasetController → Dataset
```

#### **ModelStats Service:**
```
ModelStatsRoutes → ModelStatsController → TrainingHistory
ModelStatsController → Database Views (v_model_stats_summary, etc.)
```

### 3.2 Giao tiếp giữa các services

#### **ModelStats ←→ TrainRegion:**
- ModelStats service **đồng bộ dữ liệu** từ TrainRegion
- Khi TrainRegion hoàn thành training → gửi data sang ModelStats
- ModelStats lưu vào `training_history` table

#### **Độc lập:**
- Vehicle Service hoạt động **hoàn toàn độc lập**
- TrainRegion Service **không phụ thuộc** vào các service khác
- ModelStats Service **chỉ nhận dữ liệu** từ TrainRegion

## 4. DESIGN PATTERNS ĐƯỢC SỬ DỤNG

### 4.1 **MVC Pattern (Model-View-Controller)**
- **Model**: Định nghĩa cấu trúc dữ liệu và business rules
- **Controller**: Xử lý business logic và điều phối
- **Routes**: Đóng vai trò View layer cho API

### 4.2 **Repository Pattern**
- Controllers sử dụng ORM để truy cập database
- Tách biệt business logic khỏi data access logic

### 4.3 **Microservices Pattern**
- Mỗi service có database riêng
- Giao tiếp qua REST API
- Có thể deploy và scale độc lập

### 4.4 **Observer Pattern (trong TrainRegion)**
- TrainingController theo dõi tiến độ training
- Cập nhật real-time progress
- Thông báo khi training hoàn thành

## 5. LỢI ÍCH CỦA THIẾT KẾ

### 5.1 **Tách biệt trách nhiệm (Separation of Concerns)**
- Vehicle: Quản lý thông tin xe
- TrainRegion: Training AI models
- ModelStats: Thống kê và analytics

### 5.2 **Scalability**
- Có thể scale từng service độc lập
- Thêm instance cho service có load cao

### 5.3 **Maintainability**
- Code dễ maintain vì tách biệt rõ ràng
- Bug ở service này không ảnh hưởng service khác

### 5.4 **Technology Diversity**
- Vehicle & ModelStats: Node.js (phù hợp với CRUD operations)
- TrainRegion: Python (phù hợp với AI/ML workflows)

### 5.5 **Database Independence**
- Mỗi service có database riêng
- Có thể optimize database cho từng use case

## 6. WORKFLOW TƯƠNG TÁC

### 6.1 **Training Workflow:**
```
1. User upload dataset → TrainRegion/DatasetController
2. User start training → TrainRegion/TrainingController
3. Training completed → TrainRegion/ModelController
4. Model approved → Sync to ModelStats/TrainingHistory
5. View statistics → ModelStats/ModelStatsController
```

### 6.2 **Vehicle Management Workflow:**
```
1. User add vehicle → Vehicle/VehicleController
2. System stores vehicle info → Vehicle/Vehicle Model
3. Vehicle info available for other services via API
```

**Thiết kế này đảm bảo hệ thống có tính mở rộng cao, dễ bảo trì và phù hợp với yêu cầu business.**
