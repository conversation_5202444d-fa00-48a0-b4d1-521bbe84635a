# THIẾT KẾ CÁC LỚP THỰC THỂ (ENTITY CLASSES) - HỆ THỐNG 3 SERVICES

## 1. PHƯƠNG PHÁP CHỌN RA CÁC CLASS THỰC THỂ

### 1.1 Nguyên tắc Domain-Driven Design (DDD)

#### **Bước 1: Phân tích Domain (Lĩnh vực)**
- **Vehicle Management**: <PERSON><PERSON><PERSON><PERSON> lý thông tin đăng ký xe
- **AI Model Training**: Huấn luyện mô hình nhận diện biển số
- **Model Analytics**: Thống kê hiệu suất mô hình

#### **Bước 2: <PERSON><PERSON><PERSON> định Bounded Context**
- **Vehicle Context**: Thông tin xe, chủ xe, đăng ký
- **Training Context**: Dataset, model, training job
- **Analytics Context**: Lịch sử training, thống kê

#### **Bước 3: Tì<PERSON> ra c<PERSON><PERSON> (<PERSON><PERSON> từ) chính**
- Xe (Vehicle), Dataset, Model, Training Job, Training History
- Ch<PERSON> xe (Owner), <PERSON><PERSON><PERSON><PERSON> (License Plate), Ảnh (Image)

#### **Bước 4: <PERSON><PERSON> dụng Entity vs Value Object**
- **Entity**: Có identity duy nhất, có lifecycle
- **Value Object**: Không có identity, immutable

### 1.2 Tiêu chí chọn Entity Classes

#### **Tiêu chí 1: Có Identity duy nhất**
```
✅ Vehicle (có license_plate duy nhất)
✅ Dataset (có id và name duy nhất)
✅ Model (có id duy nhất)
✅ Training_Job (có id duy nhất)
✅ Training_History (có id duy nhất)
```

#### **Tiêu chí 2: Có Lifecycle riêng**
```
✅ Vehicle: Tạo → Active → Expired/Suspended
✅ Dataset: Upload → Processing → Ready/Error
✅ Training_Job: Pending → Running → Completed/Failed/Stopped
✅ Model: Created → Approved/Rejected
✅ Training_History: Synced → Updated
```

#### **Tiêu chí 3: Có Business Rules**
```
✅ Vehicle: Validation biển số, kiểm tra hạn đăng ký
✅ Dataset: Validation format ảnh, auto-labeling
✅ Training_Job: Validation parameters, stop mechanism
✅ Model: Approval workflow, performance metrics
✅ Training_History: Data aggregation, statistics
```

## 2. CHI TIẾT TỪNG ENTITY CLASS

### 2.1 VEHICLE SERVICE

#### **Entity: VEHICLE**
```sql
Lý do chọn: Đây là core entity của vehicle management domain
Business Rules:
- license_plate phải unique trong hệ thống
- expiry_date phải > registration_date
- status tự động chuyển expired khi quá hạn
- owner_id phải valid (CCCD/CMND format)

Attributes chọn:
- id: Primary key, auto-increment
- license_plate: Business key, unique constraint
- owner_*: Thông tin chủ xe (cần thiết cho tra cứu)
- vehicle_*: Thông tin xe (cần thiết cho nhận diện)
- registration_date, expiry_date: Quản lý vòng đời
- status: Trạng thái hiện tại
- created_at, updated_at: Audit trail
```

**Tại sao không tách Owner thành entity riêng?**
- Owner không có lifecycle độc lập
- Một owner có thể có nhiều xe → tạo phức tạp không cần thiết
- Vehicle domain tập trung vào xe, không phải chủ xe

### 2.2 TRAINREGION SERVICE

#### **Entity: DATASET**
```sql
Lý do chọn: Quản lý dữ liệu training, có lifecycle riêng
Business Rules:
- Validation format ảnh (jpg, png)
- Auto-labeling với confidence threshold
- Status workflow: uploaded → processing → ready
- Tính toán image_count và file_size

Attributes chọn:
- upload_path: Đường dẫn lưu trữ file
- image_count, file_size: Metadata quan trọng
- has_labels, labeling_method: Quản lý labeling process
- labeling_accuracy: Đánh giá chất lượng auto-labeling
- status: Theo dõi quá trình xử lý
```

#### **Entity: MODEL**
```sql
Lý do chọn: Kết quả của training process, có approval workflow
Business Rules:
- Chỉ model approved mới được sử dụng production
- Performance metrics (map50, map95) để đánh giá
- Liên kết với dataset để traceability
- Version control cho model evolution

Attributes chọn:
- type: Enum YOLOv8 variants (n,s,m,l,x)
- map50, map95: Standard metrics cho object detection
- is_approved, approval_notes: Approval workflow
- dataset_id: Traceability về nguồn data
- model_path: File system location
```

#### **Entity: TRAINING_JOB**
```sql
Lý do chọn: Quản lý quá trình training, có state machine phức tạp
Business Rules:
- Real-time progress tracking
- Stop mechanism với graceful shutdown
- Error handling và logging
- Resource management (GPU, memory)

Attributes chọn:
- status: State machine (pending→running→completed/failed/stopped)
- progress, current_epoch: Real-time tracking
- can_stop, stop_requested: Stop mechanism
- best_map50, best_map95: Track best performance during training
- learning_rate, batch_size: Hyperparameters
- result_path: Output artifacts location
```

### 2.3 MODELSTATS SERVICE

#### **Entity: TRAINING_HISTORY**
```sql
Lý do chọn: Aggregate data từ TrainRegion, có business logic riêng
Business Rules:
- Đồng bộ dữ liệu từ TrainRegion khi training completed
- Tính toán composite score cho ranking
- Aggregation cho statistics views
- Historical data preservation

Attributes chọn:
- model_id: Reference về original model (không FK thực sự)
- Duplicate key attributes: model_name, model_type, etc.
- Performance metrics: map50, map95, training_time
- status: Final status của training
- created_at: Thời điểm sync (khác với training time)
```

**Tại sao duplicate data thay vì foreign key?**
- Microservices independence: ModelStats không phụ thuộc TrainRegion
- Data consistency: Nếu TrainRegion xóa model, stats vẫn giữ lại
- Performance: Tránh cross-service queries
- Historical preservation: Giữ snapshot tại thời điểm sync

## 3. MỐI QUAN HỆ GIỮA CÁC ENTITIES

### 3.1 Quan hệ nội bộ TrainRegion Service

#### **MODEL ←→ DATASET (Many-to-One)**
```sql
- Một dataset có thể train nhiều models
- Một model chỉ sử dụng một dataset
- Foreign key: model.dataset_id → dataset.id
- Business rule: Không được xóa dataset nếu có model đang sử dụng
```

#### **TRAINING_JOB ←→ MODEL (One-to-One)**
```sql
- Một training job tạo ra một model
- Một model được tạo bởi một training job
- Foreign key: training_job.model_id → model.id
- Business rule: Model chỉ được tạo khi training job completed
```

#### **TRAINING_JOB ←→ DATASET (Many-to-One)**
```sql
- Một dataset có thể được sử dụng cho nhiều training jobs
- Một training job chỉ sử dụng một dataset
- Foreign key: training_job.dataset_id → dataset.id
- Business rule: Dataset phải ở status 'ready' mới được train
```

### 3.2 Quan hệ cross-service

#### **MODEL (TrainRegion) ⇒ TRAINING_HISTORY (ModelStats)**
```sql
- Không phải foreign key thực sự
- Data synchronization khi model approved
- One-way data flow: TrainRegion → ModelStats
- Eventual consistency model
```

## 4. DESIGN DECISIONS VÀ TRADE-OFFS

### 4.1 Tại sao không có User/Account entities?

**Decision**: Sử dụng external Auth Service
**Reasoning**:
- Authentication/Authorization là cross-cutting concern
- Tránh duplicate user management logic
- Single Sign-On (SSO) cho tất cả services
- Separation of concerns: Business logic vs Security

### 4.2 Tại sao ModelStats duplicate data?

**Decision**: Data duplication thay vì normalization
**Trade-offs**:
- ✅ Service independence
- ✅ Query performance
- ✅ Historical data preservation
- ❌ Storage overhead
- ❌ Data synchronization complexity

### 4.3 Tại sao không có Image entity?

**Decision**: Images được quản lý như files, không phải entities
**Reasoning**:
- Images không có business logic phức tạp
- File system đã cung cấp metadata (size, timestamp)
- Tránh overhead của database storage cho binary data
- Dataset entity đã track image_count và file_size

### 4.4 Tại sao TrainingJob và Model tách riêng?

**Decision**: Tách Job (process) và Model (result)
**Benefits**:
- Clear separation: Process vs Product
- Job có thể fail mà không tạo Model
- Model có approval workflow riêng
- Easier to track training metrics vs model metrics

## 5. VALIDATION RULES VÀ CONSTRAINTS

### 5.1 Business Rules Implementation

#### **Vehicle Entity**
```sql
- license_plate: Regex validation theo format VN
- expiry_date > registration_date
- status auto-update based on expiry_date
- owner_id: CCCD/CMND format validation
```

#### **Dataset Entity**
```sql
- image_count > 0 khi status = 'ready'
- labeling_accuracy between 0.0 and 1.0
- upload_path must exist and accessible
- file_size > 0
```

#### **Training_Job Entity**
```sql
- epochs between 1 and 1000
- batch_size power of 2, between 1 and 128
- learning_rate between 0.0001 and 1.0
- progress between 0.0 and 1.0
- current_epoch <= epochs
```

#### **Model Entity**
```sql
- map50, map95 between 0.0 and 1.0
- training_time > 0
- model_path file must exist
- approval_notes required when is_approved = false
```

## 6. IMPLEMENTATION EXAMPLES

### 6.1 Vehicle Entity (Node.js + Sequelize)
```javascript
// models/Vehicle.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Vehicle = sequelize.define('Vehicle', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    licensePlate: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        isValidLicensePlate(value) {
          // Vietnam license plate format validation
          const regex = /^[0-9]{2}[A-Z]{1,2}-[0-9]{3,4}\.[0-9]{2}$/;
          if (!regex.test(value)) {
            throw new Error('Invalid license plate format');
          }
        }
      }
    },
    ownerName: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('active', 'expired', 'suspended'),
      defaultValue: 'active'
    }
  }, {
    hooks: {
      beforeSave: (vehicle) => {
        // Auto-update status based on expiry date
        if (vehicle.expiryDate < new Date()) {
          vehicle.status = 'expired';
        }
      }
    }
  });

  return Vehicle;
};
```

### 6.2 Model Entity (Python + SQLAlchemy)
```python
# models/model.py
from sqlalchemy import Column, Integer, String, Float, Boolean, Text, ForeignKey
from sqlalchemy.orm import validates
from config.config import Base

class Model(Base):
    __tablename__ = 'models'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    type = Column(Enum('yolov8n', 'yolov8s', 'yolov8m', 'yolov8l', 'yolov8x'), nullable=False)
    map50 = Column(Float, nullable=False)
    map95 = Column(Float, nullable=False)
    is_approved = Column(Boolean, default=False)
    dataset_id = Column(Integer, ForeignKey('datasets.id'))

    @validates('map50', 'map95')
    def validate_map_scores(self, key, value):
        if not 0.0 <= value <= 1.0:
            raise ValueError(f'{key} must be between 0.0 and 1.0')
        return value

    def approve(self, notes=None):
        """Business method for model approval"""
        self.is_approved = True
        self.approval_notes = notes

    def calculate_composite_score(self):
        """Calculate composite performance score"""
        return self.map50 * 0.7 + self.map95 * 0.3
```

### 6.3 Training History Entity (Node.js + Sequelize)
```javascript
// models/TrainingHistory.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const TrainingHistory = sequelize.define('TrainingHistory', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    modelId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Reference to model in TrainRegion service'
    },
    modelName: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    modelType: {
      type: DataTypes.ENUM('yolov8n', 'yolov8s', 'yolov8m', 'yolov8l', 'yolov8x'),
      allowNull: false
    },
    map50: DataTypes.FLOAT,
    map95: DataTypes.FLOAT,
    status: {
      type: DataTypes.ENUM('completed', 'failed', 'stopped'),
      allowNull: false
    }
  }, {
    indexes: [
      { fields: ['modelType'] },
      { fields: ['status'] },
      { fields: ['createdAt'] }
    ],
    scopes: {
      completed: {
        where: { status: 'completed' }
      },
      topPerformers: {
        where: { status: 'completed' },
        order: [
          [sequelize.literal('(map50 * 0.7 + map95 * 0.3)'), 'DESC']
        ],
        limit: 10
      }
    }
  });

  // Instance method
  TrainingHistory.prototype.getCompositeScore = function() {
    return this.map50 * 0.7 + this.map95 * 0.3;
  };

  return TrainingHistory;
};
```

**Thiết kế này đảm bảo:**
- ✅ **Domain-driven**: Phản ánh đúng business domain
- ✅ **Scalable**: Có thể mở rộng từng service độc lập
- ✅ **Maintainable**: Logic rõ ràng, dễ hiểu
- ✅ **Performant**: Optimized cho use cases chính
- ✅ **Validated**: Business rules được enforce ở entity level
- ✅ **Indexed**: Database performance optimization
