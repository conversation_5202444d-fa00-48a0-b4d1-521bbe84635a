<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dự đoán biển số xe</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand { font-weight: bold; }
        .result-img { max-width: 100%; border: 2px solid #0d6efd; margin-top: 20px; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">Hệ thống quản lý biển số xe</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"><a class="nav-link" href="dashboard.html">Danh sách xe</a></li>
                    <li class="nav-item"><a class="nav-link" href="add-vehicle.html">Thêm xe mới</a></li>
                    <li class="nav-item"><a class="nav-link active" href="predict.html">Nhận diện biển số</a></li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-light me-3" id="user-info"></span>
                    <button class="btn btn-outline-light" id="logout-btn">Đăng xuất</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2 class="mb-4">Nhận diện biển số xe từ ảnh</h2>
        <form id="predict-form">
            <div class="mb-3">
                <label for="image" class="form-label">Chọn ảnh</label>
                <input class="form-control" type="file" id="image" accept="image/*" required />
            </div>
            <button type="submit" class="btn btn-primary">Dự đoán</button>
        </form>
        <div id="predict-result" class="mt-4"></div>
    </div>

    <script src="js/api.js"></script>
    <script>
        // Check login
        const user = JSON.parse(localStorage.getItem("user"));
        const token = localStorage.getItem("token");
        if (!user || !token) {
            window.location.href = "login.html";
        }
        document.getElementById("user-info").textContent = `Xin chào, ${user.fullName} (${user.role === "admin" ? "Quản trị viên" : "Cảnh sát"})`;
        document.getElementById("logout-btn").onclick = function() {
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.location.href = "login.html";
        };

        // Predict form submit
        document.getElementById("predict-form").addEventListener("submit", async function(e) {
            e.preventDefault();
            const fileInput = document.getElementById("image");
            const file = fileInput.files[0];
            if (!file) return;
            const resultDiv = document.getElementById("predict-result");
            resultDiv.innerHTML = '<div class="text-info">Đang xử lý...</div>';
            try {
                const formData = new FormData();
                formData.append("image", file);
                // Gọi API predict qua api-gateway
                const response = await fetch("http://localhost:3000/api/predict", {
                    method: "POST",
                    headers: { "x-auth-token": token },
                    body: formData
                });
                if (!response.ok) {
                    const err = await response.json();
                    throw new Error(err.message || "Lỗi dự đoán");
                }
                const data = await response.json();
                let html = `<div class='alert alert-success'>${data.message || "Nhận diện thành công!"}</div>`;
                if (data.result_image_url) {
                    html += `<img src="${data.result_image_url}" class="result-img" alt="Kết quả nhận diện" />`;
                }
                if (data.boxes && data.boxes.length > 0) {
                    html += `<div class='mt-2'><b>Vùng biển số phát hiện:</b><ul>`;
                    data.boxes.forEach((box, i) => {
                        html += `<li>Box ${i+1}: [${box.join(", ")}]</li>`;
                    });
                    html += `</ul></div>`;
                }
                resultDiv.innerHTML = html;
            } catch (err) {
                resultDiv.innerHTML = `<div class='alert alert-danger'>${err.message}</div>`;
            }
        });
    </script>
</body>
</html>
