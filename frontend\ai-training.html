<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Training - H<PERSON> thống quản lý bi<PERSON>n số xe</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #fff;
            padding: 15px 20px;
        }
        .sidebar .nav-link:hover {
            background-color: #495057;
            color: #fff;
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: #fff;
        }
        .main-content {
            padding: 20px;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .model-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .training-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">Hệ thống quản lý</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="fas fa-tachometer-alt me-2"></i>Tổng quan
                            </a>
                        </li>
                        <li class="nav-item" id="add-vehicle-nav">
                            <a class="nav-link" href="add-vehicle.html">
                                <i class="fas fa-plus me-2"></i>Thêm xe mới
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="ai-training.html">
                                <i class="fas fa-brain me-2"></i>Huấn luyện AI
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="ai-predict.html">
                                <i class="fas fa-search me-2"></i>Dự đoán AI
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="model-stats.html">
                                <i class="fas fa-chart-bar me-2"></i>Thống kê mô hình
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4 px-3">
                        <div class="text-white-50 small">
                            <div id="user-info-sidebar"></div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Huấn luyện AI</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>Làm mới
                        </button>
                    </div>
                </div>

                <!-- Dataset Upload Section -->
                <div class="training-form">
                    <h4>Upload Dataset</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Upload Images Tab -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Upload Ảnh (Auto-labeling)</h6>
                                </div>
                                <div class="card-body">
                                    <form id="upload-images-form">
                                        <div class="mb-3">
                                            <label for="dataset-name" class="form-label">Tên Dataset</label>
                                            <input type="text" class="form-control" id="dataset-name" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="labeling-method" class="form-label">Phương pháp labeling</label>
                                            <select class="form-select" id="labeling-method">
                                                <option value="auto">Auto-labeling (AI tự động)</option>
                                                <option value="template">Template-based (Vị trí cố định)</option>
                                                <option value="manual">Manual (Tự label sau)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3" id="confidence-section">
                                            <label for="confidence-threshold" class="form-label">Confidence Threshold</label>
                                            <input type="range" class="form-range" id="confidence-threshold" min="0.1" max="0.9" step="0.1" value="0.3">
                                            <small class="text-muted">Giá trị: <span id="confidence-value">0.3</span></small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="image-files" class="form-label">Chọn ảnh</label>
                                            <input type="file" class="form-control" id="image-files" multiple accept="image/*" required>
                                            <small class="text-muted">Chọn nhiều ảnh định dạng JPG, PNG</small>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-upload me-1"></i>Upload Dataset
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Upload ZIP Tab -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Upload ZIP Dataset</h6>
                                </div>
                                <div class="card-body">
                                    <form id="upload-zip-form">
                                        <div class="mb-3">
                                            <label for="zip-dataset-name" class="form-label">Tên Dataset</label>
                                            <input type="text" class="form-control" id="zip-dataset-name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="zip-dataset-description" class="form-label">Mô tả (tùy chọn)</label>
                                            <textarea class="form-control" id="zip-dataset-description" rows="2"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="zip-file" class="form-label">Chọn file ZIP</label>
                                            <input type="file" class="form-control" id="zip-file" accept=".zip" required>
                                            <small class="text-muted">
                                                ZIP phải chứa: images/, labels/, classes.txt
                                            </small>
                                        </div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-file-archive me-1"></i>Upload ZIP
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Datasets List -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Datasets</h5>
                    </div>
                    <div class="card-body">
                        <div id="datasets-container">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Training Form -->
                <div class="training-form">
                    <h4>Tạo Model Mới</h4>
                    <form id="training-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-name" class="form-label">Tên Model</label>
                                    <input type="text" class="form-control" id="model-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dataset-select" class="form-label">Chọn Dataset</label>
                                    <select class="form-select" id="dataset-select" required>
                                        <option value="">Chọn dataset để training</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-type" class="form-label">Loại Model</label>
                                    <select class="form-select" id="model-type" required>
                                        <option value="">Chọn loại model</option>
                                        <option value="yolov8n">YOLOv8 Nano (Nhanh)</option>
                                        <option value="yolov8s">YOLOv8 Small</option>
                                        <option value="yolov8m">YOLOv8 Medium</option>
                                        <option value="yolov8l">YOLOv8 Large</option>
                                        <option value="yolov8x">YOLOv8 Extra Large (Chính xác)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="epochs" class="form-label">Epochs</label>
                                    <input type="number" class="form-control" id="epochs" min="1" max="1000" value="50" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="batch-size" class="form-label">Batch Size</label>
                                    <input type="number" class="form-control" id="batch-size" min="1" max="64" value="8" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="learning-rate" class="form-label">Learning Rate</label>
                                    <input type="number" class="form-control" id="learning-rate" min="0.001" max="0.1" step="0.001" value="0.01" required>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-play me-1"></i>Bắt đầu Training
                        </button>
                    </form>
                </div>

                <!-- Training Jobs -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Training Jobs</h5>
                    </div>
                    <div class="card-body">
                        <div id="training-jobs-container">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Models -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Trained Models</h5>
                    </div>
                    <div class="card-body">
                        <div id="models-container">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script>
        // Check authentication
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user) {
            window.location.href = 'login.html';
        }

        // Update UI based on user role
        document.getElementById("user-info-sidebar").innerHTML = `
            <strong>${user.fullName}</strong><br>
            <small>${user.role === "admin" ? "Quản trị viên" : "Cảnh sát"}</small>
        `;

        // Only show Add Vehicle button for admin
        if (user.role !== "admin") {
            document.getElementById("add-vehicle-nav").style.display = "none";
        }

        // Logout function
        function logout() {
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            window.location.href = 'login.html';
        }

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadDatasets();
            loadTrainingJobs();
            loadModels();

            // Setup confidence threshold display
            const confidenceSlider = document.getElementById('confidence-threshold');
            const confidenceValue = document.getElementById('confidence-value');
            confidenceSlider.addEventListener('input', function() {
                confidenceValue.textContent = this.value;
            });

            // Setup labeling method change
            document.getElementById('labeling-method').addEventListener('change', function() {
                const confidenceSection = document.getElementById('confidence-section');
                if (this.value === 'auto') {
                    confidenceSection.style.display = 'block';
                } else {
                    confidenceSection.style.display = 'none';
                }
            });
        });

        // Upload images form submission
        document.getElementById('upload-images-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            const files = document.getElementById('image-files').files;

            if (files.length === 0) {
                alert('Vui lòng chọn ít nhất một ảnh');
                return;
            }

            // Add files
            for (let file of files) {
                formData.append('images', file);
            }

            // Add other data
            formData.append('name', document.getElementById('dataset-name').value);
            formData.append('description', ''); // Empty description for images upload
            formData.append('method', document.getElementById('labeling-method').value);
            formData.append('confidence_threshold', document.getElementById('confidence-threshold').value);

            try {
                const response = await fetch('http://localhost:3003/api/dataset/upload-images', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert(`Dataset uploaded successfully! ${result.labeling_result.labeled_images}/${result.labeling_result.total_images} images labeled.`);
                    document.getElementById('upload-images-form').reset();
                    loadDatasets();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error uploading dataset: ' + error.message);
            }
        });

        // Upload ZIP form submission
        document.getElementById('upload-zip-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            const file = document.getElementById('zip-file').files[0];

            if (!file) {
                alert('Vui lòng chọn file ZIP');
                return;
            }

            formData.append('dataset', file);
            formData.append('name', document.getElementById('zip-dataset-name').value);
            formData.append('description', document.getElementById('zip-dataset-description').value);

            try {
                const response = await fetch('http://localhost:3003/api/dataset/upload-zip', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert('ZIP dataset uploaded successfully!');
                    document.getElementById('upload-zip-form').reset();
                    loadDatasets();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error uploading ZIP: ' + error.message);
            }
        });

        // Training form submission
        document.getElementById('training-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('model-name').value,
                dataset_id: parseInt(document.getElementById('dataset-select').value),
                model_type: document.getElementById('model-type').value,
                epochs: parseInt(document.getElementById('epochs').value),
                batch_size: parseInt(document.getElementById('batch-size').value),
                learning_rate: parseFloat(document.getElementById('learning-rate').value)
            };

            try {
                const response = await fetch('http://localhost:3003/api/training/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    alert('Training started successfully!');
                    document.getElementById('training-form').reset();
                    loadTrainingJobs();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error starting training: ' + error.message);
            }
        });

        // Load datasets
        async function loadDatasets() {
            try {
                const response = await fetch('http://localhost:3003/api/dataset/');
                const result = await response.json();

                if (result.success) {
                    displayDatasets(result.datasets);
                    updateDatasetSelect(result.datasets);
                } else {
                    document.getElementById('datasets-container').innerHTML =
                        '<div class="alert alert-danger">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('datasets-container').innerHTML =
                    '<div class="alert alert-danger">Error loading datasets: ' + error.message + '</div>';
            }
        }

        // Load training jobs
        async function loadTrainingJobs() {
            try {
                const response = await fetch('http://localhost:3003/api/training/jobs');
                const result = await response.json();

                if (result.success) {
                    displayTrainingJobs(result.training_jobs);
                } else {
                    document.getElementById('training-jobs-container').innerHTML =
                        '<div class="alert alert-danger">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('training-jobs-container').innerHTML =
                    '<div class="alert alert-danger">Error loading training jobs: ' + error.message + '</div>';
            }
        }

        // Load models
        async function loadModels() {
            try {
                const response = await fetch('http://localhost:3003/api/model/');
                const result = await response.json();

                if (result.success) {
                    displayModels(result.models);
                } else {
                    document.getElementById('models-container').innerHTML =
                        '<div class="alert alert-danger">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('models-container').innerHTML =
                    '<div class="alert alert-danger">Error loading models: ' + error.message + '</div>';
            }
        }

        // Display datasets
        function displayDatasets(datasets) {
            const container = document.getElementById('datasets-container');

            if (datasets.length === 0) {
                container.innerHTML = '<p class="text-muted">No datasets found.</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
            html += '<th>ID</th><th>Name</th><th>Type</th><th>Images</th><th>Labeling</th><th>Status</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            datasets.forEach(dataset => {
                const statusClass = {
                    'uploaded': 'warning',
                    'processing': 'info',
                    'ready': 'success',
                    'error': 'danger'
                }[dataset.status] || 'secondary';

                const labelingBadge = dataset.has_labels ?
                    `<span class="badge bg-success">${dataset.labeling_method}</span>` :
                    '<span class="badge bg-warning">No labels</span>';

                html += `<tr>
                    <td>${dataset.id}</td>
                    <td>${dataset.name}</td>
                    <td><span class="badge bg-info">${dataset.upload_type}</span></td>
                    <td>${dataset.image_count}</td>
                    <td>${labelingBadge}</td>
                    <td><span class="badge bg-${statusClass}">${dataset.status}</span></td>
                    <td>${new Date(dataset.created_at).toLocaleString()}</td>
                    <td>
                        <button class="btn btn-sm btn-danger" onclick="deleteDataset(${dataset.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // Update dataset select dropdown
        function updateDatasetSelect(datasets) {
            const select = document.getElementById('dataset-select');

            // Clear existing options except first one
            select.innerHTML = '<option value="">Chọn dataset để training</option>';

            // Add ready datasets
            datasets.filter(d => d.status === 'ready').forEach(dataset => {
                const option = document.createElement('option');
                option.value = dataset.id;
                option.textContent = `${dataset.name} (${dataset.image_count} images, ${dataset.labeling_method})`;
                select.appendChild(option);
            });
        }

        // Delete dataset
        async function deleteDataset(datasetId) {
            if (!confirm('Bạn có chắc muốn xóa dataset này?')) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:3003/api/dataset/${datasetId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    alert('Dataset deleted successfully!');
                    loadDatasets();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error deleting dataset: ' + error.message);
            }
        }

        // Display training jobs
        function displayTrainingJobs(jobs) {
            const container = document.getElementById('training-jobs-container');

            if (jobs.length === 0) {
                container.innerHTML = '<p class="text-muted">No training jobs found.</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
            html += '<th>ID</th><th>Name</th><th>Dataset</th><th>Model Type</th><th>Status</th><th>Progress</th><th>mAP50</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            jobs.forEach(job => {
                const statusClass = {
                    'pending': 'warning',
                    'running': 'info',
                    'completed': 'success',
                    'failed': 'danger',
                    'stopped': 'secondary'
                }[job.status] || 'secondary';

                const progressBar = job.status === 'running' ?
                    `<div class="progress" style="width: 100px;">
                        <div class="progress-bar" role="progressbar" style="width: ${job.progress}%">${job.progress.toFixed(1)}%</div>
                    </div>` :
                    `${job.progress.toFixed(1)}%`;

                let actionButtons = '';

                if (job.status === 'running') {
                    actionButtons = `<button class="btn btn-sm btn-warning" onclick="stopTrainingJob(${job.id})">
                        <i class="fas fa-stop"></i> Stop
                    </button>`;
                } else if (job.status === 'completed' && !job.model_saved) {
                    actionButtons = `<button class="btn btn-sm btn-success me-1" onclick="reviewAndSaveModel(${job.id})">
                        <i class="fas fa-check"></i> Review & Save
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="discardTrainingResult(${job.id})">
                        <i class="fas fa-trash"></i> Discard
                    </button>`;
                } else if (job.model_saved) {
                    actionButtons = '<span class="badge bg-info">Model Saved</span>';
                }

                html += `<tr>
                    <td>${job.id}</td>
                    <td>${job.name || 'N/A'}</td>
                    <td>${job.dataset_info ? job.dataset_info.name : 'N/A'}</td>
                    <td>${job.model_type}</td>
                    <td><span class="badge bg-${statusClass}">${job.status}</span></td>
                    <td>${progressBar}</td>
                    <td>${job.best_map50 ? job.best_map50.toFixed(3) : 'N/A'}</td>
                    <td>${new Date(job.created_at).toLocaleString()}</td>
                    <td>${actionButtons}</td>
                </tr>`;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // Display models
        function displayModels(models) {
            const container = document.getElementById('models-container');

            if (models.length === 0) {
                container.innerHTML = '<p class="text-muted">No models found.</p>';
                return;
            }

            let html = '';
            models.forEach(model => {
                const approvalBadge = model.is_approved ?
                    '<span class="badge bg-success">Approved</span>' :
                    '<span class="badge bg-warning">Pending</span>';

                html += `<div class="model-card">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>${model.name} ${approvalBadge}</h6>
                            <p class="mb-1"><strong>Type:</strong> ${model.type}</p>
                            <p class="mb-1"><strong>Epochs:</strong> ${model.epochs} | <strong>Batch Size:</strong> ${model.batch_size}</p>
                            <p class="mb-1"><strong>Training Time:</strong> ${model.training_time ? model.training_time.toFixed(2) + 's' : 'N/A'}</p>
                            <p class="mb-1"><strong>Dataset:</strong> ${model.dataset_info ? model.dataset_info.name : 'N/A'}</p>
                            <p class="mb-0"><strong>Created:</strong> ${new Date(model.created_at).toLocaleString()}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <p class="mb-1"><strong>mAP50:</strong> ${model.map50 ? model.map50.toFixed(3) : 'N/A'}</p>
                            <p class="mb-1"><strong>mAP95:</strong> ${model.map95 ? model.map95.toFixed(3) : 'N/A'}</p>
                            <span class="badge ${model.file_exists ? 'bg-success' : 'bg-danger'}">
                                ${model.file_exists ? 'Available' : 'Missing'}
                            </span>
                            <div class="mt-2">
                                ${!model.is_approved ? `
                                    <button class="btn btn-sm btn-success me-1" onclick="approveModel(${model.id})">
                                        <i class="fas fa-check"></i> Approve
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="rejectModel(${model.id})">
                                        <i class="fas fa-times"></i> Reject
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>`;
            });

            container.innerHTML = html;
        }

        // Approve model
        async function approveModel(modelId) {
            const notes = prompt('Approval notes (optional):');

            try {
                const response = await fetch(`http://localhost:3003/api/model/${modelId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ notes: notes || '' })
                });

                const result = await response.json();

                if (result.success) {
                    alert('Model approved successfully!');
                    loadModels();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error approving model: ' + error.message);
            }
        }

        // Reject model
        async function rejectModel(modelId) {
            const reason = prompt('Rejection reason:');
            if (!reason) return;

            try {
                const response = await fetch(`http://localhost:3003/api/model/${modelId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ reason: reason })
                });

                const result = await response.json();

                if (result.success) {
                    alert('Model rejected successfully!');
                    loadModels();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error rejecting model: ' + error.message);
            }
        }

        // Review and save model after training completion
        async function reviewAndSaveModel(jobId) {
            try {
                // Get training job details
                const jobResponse = await fetch(`http://localhost:3003/api/training/jobs/${jobId}`);
                const jobResult = await jobResponse.json();

                if (!jobResult.success) {
                    alert('Error getting job details: ' + jobResult.error);
                    return;
                }

                const job = jobResult.training_job;

                // Show review modal
                const reviewHtml = `
                    <div class="modal fade" id="reviewModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Review Training Results</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Training Information</h6>
                                            <p><strong>Job Name:</strong> ${job.name}</p>
                                            <p><strong>Model Type:</strong> ${job.model_type}</p>
                                            <p><strong>Epochs:</strong> ${job.epochs}</p>
                                            <p><strong>Batch Size:</strong> ${job.batch_size}</p>
                                            <p><strong>Training Time:</strong> ${job.training_time.toFixed(2)}s</p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Performance Metrics</h6>
                                            <p><strong>mAP50:</strong> ${job.best_map50.toFixed(3)}</p>
                                            <p><strong>mAP95:</strong> ${job.best_map95.toFixed(3)}</p>
                                            <p><strong>Final Epoch:</strong> ${job.current_epoch}</p>
                                            <p><strong>Progress:</strong> ${job.progress.toFixed(1)}%</p>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <h6>Quality Assessment</h6>
                                        <div class="alert ${job.best_map50 >= 0.8 ? 'alert-success' : job.best_map50 >= 0.6 ? 'alert-warning' : 'alert-danger'}">
                                            ${job.best_map50 >= 0.8 ? '✅ Excellent performance (mAP50 ≥ 0.8)' :
                                              job.best_map50 >= 0.6 ? '⚠️ Good performance (mAP50 ≥ 0.6)' :
                                              '❌ Poor performance (mAP50 < 0.6)'}
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <label for="modelName" class="form-label">Model Name:</label>
                                        <input type="text" class="form-control" id="modelName" value="${job.name}_model_v1.0">
                                    </div>

                                    <div class="mt-3">
                                        <label for="modelNotes" class="form-label">Notes (optional):</label>
                                        <textarea class="form-control" id="modelNotes" rows="3" placeholder="Add any notes about this model..."></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" onclick="discardTrainingResult(${jobId})" data-bs-dismiss="modal">
                                        <i class="fas fa-trash"></i> Discard
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="saveModelToDatabase(${jobId})">
                                        <i class="fas fa-save"></i> Save Model
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                const existingModal = document.getElementById('reviewModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Add modal to page
                document.body.insertAdjacentHTML('beforeend', reviewHtml);

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
                modal.show();

            } catch (error) {
                alert('Error reviewing model: ' + error.message);
            }
        }

        // Save model to database after review
        async function saveModelToDatabase(jobId) {
            const modelName = document.getElementById('modelName').value;
            const notes = document.getElementById('modelNotes').value;

            if (!modelName.trim()) {
                alert('Please enter a model name');
                return;
            }

            try {
                const response = await fetch(`http://localhost:3003/api/training/jobs/${jobId}/save-model`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model_name: modelName.trim(),
                        notes: notes.trim()
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert('Model saved successfully!');

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('reviewModal'));
                    modal.hide();

                    // Refresh data
                    loadTrainingJobs();
                    loadModels();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error saving model: ' + error.message);
            }
        }

        // Stop training job
        async function stopTrainingJob(jobId) {
            if (!confirm('Bạn có chắc muốn dừng training job này? Quá trình training sẽ bị hủy.')) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:3003/api/training/jobs/${jobId}/stop`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    alert('Training job đã được dừng thành công!');
                    loadTrainingJobs();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error stopping training: ' + error.message);
            }
        }

        // Discard training result
        async function discardTrainingResult(jobId) {
            if (!confirm('Are you sure you want to discard this training result? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:3003/api/training/jobs/${jobId}/discard`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    alert('Training result discarded successfully!');
                    loadTrainingJobs();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error discarding result: ' + error.message);
            }
        }

        // Refresh data
        function refreshData() {
            loadDatasets();
            loadTrainingJobs();
            loadModels();
        }

        // Auto refresh every 30 seconds
        setInterval(refreshData, 30000);
    </script>
</body>
</html>


