// Fake data cho giao diện thống kê mô hình
const fakeModelStats = {
    // D<PERSON> liệu thống kê hiệu suất mô hình
    modelPerformanceStats: {
        model_type_stats: [
            { type: "YOLOv5s", count: 8, avg_map50: 0.86, avg_map95: 0.62, avg_training_time: 3.2 },
            { type: "YOLOv5m", count: 6, avg_map50: 0.89, avg_map95: 0.67, avg_training_time: 4.5 },
            { type: "YOLOv5l", count: 5, avg_map50: 0.91, avg_map95: 0.72, avg_training_time: 6.8 },
            { type: "YOLOv8n", count: 3, avg_map50: 0.84, avg_map95: 0.59, avg_training_time: 2.7 },
            { type: "YOLOv8s", count: 2, avg_map50: 0.88, avg_map95: 0.65, avg_training_time: 3.9 }
        ],
        approval_stats: [
            { is_approved: true, count: 18 },
            { is_approved: false, count: 6 }
        ],
        training_time_stats: [
            { type: "YOLOv5s", avg_training_time: 3.2 },
            { type: "YOLOv5m", avg_training_time: 4.5 },
            { type: "YOLOv5l", avg_training_time: 6.8 },
            { type: "YOLOv8n", avg_training_time: 2.7 },
            { type: "YOLOv8s", avg_training_time: 3.9 }
        ],
        most_used_models: [
            { id: 1, name: "license_plate_detector_v3", type: "YOLOv5l", map50: 0.92, usage_count: 487, is_approved: true },
            { id: 2, name: "license_plate_detector_v2", type: "YOLOv5m", map50: 0.89, usage_count: 356, is_approved: true },
            { id: 3, name: "license_plate_detector_v1", type: "YOLOv5s", map50: 0.85, usage_count: 214, is_approved: true },
            { id: 4, name: "license_plate_detector_night", type: "YOLOv5l", map50: 0.88, usage_count: 98, is_approved: true },
            { id: 5, name: "license_plate_detector_rain", type: "YOLOv5m", map50: 0.86, usage_count: 90, is_approved: true }
        ],
        best_model: {
            id: 1,
            name: "license_plate_detector_v3",
            type: "YOLOv5l",
            map50: 0.92,
            map95: 0.74,
            training_time: 410, // phút
            dataset_name: "Vietnam License Plates 2023",
            created_at: "2023-09-15T08:30:00Z",
            is_approved: true
        }
    },

    // Dữ liệu xu hướng training
    trainingTrends: {
        monthly_jobs: [
            { month: "01/2023", job_count: 2, avg_map50: 0.81 },
            { month: "02/2023", job_count: 3, avg_map50: 0.82 },
            { month: "03/2023", job_count: 2, avg_map50: 0.83 },
            { month: "04/2023", job_count: 4, avg_map50: 0.84 },
            { month: "05/2023", job_count: 3, avg_map50: 0.85 },
            { month: "06/2023", job_count: 5, avg_map50: 0.86 },
            { month: "07/2023", job_count: 4, avg_map50: 0.87 },
            { month: "08/2023", job_count: 6, avg_map50: 0.88 },
            { month: "09/2023", job_count: 5, avg_map50: 0.89 },
            { month: "10/2023", job_count: 7, avg_map50: 0.90 },
            { month: "11/2023", job_count: 6, avg_map50: 0.91 },
            { month: "12/2023", job_count: 8, avg_map50: 0.92 }
        ],
        map_trends: [
            { month: "01/2023", avg_map50: 0.81 },
            { month: "02/2023", avg_map50: 0.82 },
            { month: "03/2023", avg_map50: 0.83 },
            { month: "04/2023", avg_map50: 0.84 },
            { month: "05/2023", avg_map50: 0.85 },
            { month: "06/2023", avg_map50: 0.86 },
            { month: "07/2023", avg_map50: 0.87 },
            { month: "08/2023", avg_map50: 0.88 },
            { month: "09/2023", avg_map50: 0.89 },
            { month: "10/2023", avg_map50: 0.90 },
            { month: "11/2023", avg_map50: 0.91 },
            { month: "12/2023", avg_map50: 0.92 }
        ],
        status_trends: [
            { status: "completed", count: 42 },
            { status: "failed", count: 8 },
            { status: "pending", count: 2 },
            { status: "running", count: 3 },
            { status: "stopped", count: 5 }
        ]
    },

    // Dữ liệu sử dụng prediction
    predictionUsage: {
        daily_predictions: [
            { date: "2023-12-01", count: 35 },
            { date: "2023-12-02", count: 42 },
            { date: "2023-12-03", count: 38 },
            { date: "2023-12-04", count: 56 },
            { date: "2023-12-05", count: 61 },
            { date: "2023-12-06", count: 48 },
            { date: "2023-12-07", count: 52 },
            { date: "2023-12-08", count: 45 },
            { date: "2023-12-09", count: 39 },
            { date: "2023-12-10", count: 41 },
            { date: "2023-12-11", count: 58 },
            { date: "2023-12-12", count: 63 },
            { date: "2023-12-13", count: 57 },
            { date: "2023-12-14", count: 62 }
        ],
        avg_processing_time: [
            { model_name: "license_plate_detector_v3", avg_time: 156 },
            { model_name: "license_plate_detector_v2", avg_time: 142 },
            { model_name: "license_plate_detector_v1", avg_time: 128 },
            { model_name: "license_plate_detector_night", avg_time: 163 },
            { model_name: "license_plate_detector_rain", avg_time: 151 }
        ],
        total_predictions: 1245
    },

    // Dữ liệu chi tiết mô hình
    modelDetails: [
        {
            id: 1,
            name: "license_plate_detector_v3",
            type: "YOLOv5l",
            map50: 0.92,
            map95: 0.74,
            precision: 0.95,
            recall: 0.91,
            f1_score: 0.93,
            training_time: 410,
            epochs: 100,
            batch_size: 16,
            dataset_name: "Vietnam License Plates 2023",
            dataset_size: 5200,
            created_at: "2023-09-15T08:30:00Z",
            is_approved: true,
            usage_count: 487,
            avg_processing_time: 156
        },
        {
            id: 2,
            name: "license_plate_detector_v2",
            type: "YOLOv5m",
            map50: 0.89,
            map95: 0.68,
            precision: 0.92,
            recall: 0.88,
            f1_score: 0.90,
            training_time: 320,
            epochs: 100,
            batch_size: 16,
            dataset_name: "Vietnam License Plates 2022",
            dataset_size: 4800,
            created_at: "2023-06-22T10:15:00Z",
            is_approved: true,
            usage_count: 356,
            avg_processing_time: 142
        },
        {
            id: 3,
            name: "license_plate_detector_v1",
            type: "YOLOv5s",
            map50: 0.85,
            map95: 0.63,
            precision: 0.89,
            recall: 0.84,
            f1_score: 0.86,
            training_time: 240,
            epochs: 100,
            batch_size: 32,
            dataset_name: "Vietnam License Plates 2021",
            dataset_size: 3500,
            created_at: "2023-03-10T14:45:00Z",
            is_approved: true,
            usage_count: 214,
            avg_processing_time: 128
        },
        {
            id: 4,
            name: "license_plate_detector_night",
            type: "YOLOv5l",
            map50: 0.88,
            map95: 0.67,
            precision: 0.90,
            recall: 0.87,
            f1_score: 0.88,
            training_time: 380,
            epochs: 120,
            batch_size: 16,
            dataset_name: "Night License Plates",
            dataset_size: 2800,
            created_at: "2023-07-05T16:20:00Z",
            is_approved: true,
            usage_count: 98,
            avg_processing_time: 163
        },
        {
            id: 5,
            name: "license_plate_detector_rain",
            type: "YOLOv5m",
            map50: 0.86,
            map95: 0.64,
            precision: 0.88,
            recall: 0.85,
            f1_score: 0.86,
            training_time: 300,
            epochs: 100,
            batch_size: 16,
            dataset_name: "Rainy Conditions Dataset",
            dataset_size: 2200,
            created_at: "2023-08-18T09:10:00Z",
            is_approved: true,
            usage_count: 90,
            avg_processing_time: 151
        }
    ]
};

// Cập nhật API để sử dụng fake data
API.stats = {
    getModelPerformanceStats: async () => {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    success: true,
                    stats: fakeModelStats.modelPerformanceStats
                });
            }, 500);
        });
    },
    
    getTrainingTrends: async () => {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    success: true,
                    trends: fakeModelStats.trainingTrends
                });
            }, 700);
        });
    },
    
    getPredictionUsage: async () => {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    success: true,
                    usage: fakeModelStats.predictionUsage
                });
            }, 600);
        });
    },
    
    getModelDetails: async (modelId) => {
        return new Promise(resolve => {
            setTimeout(() => {
                const model = fakeModelStats.modelDetails.find(m => m.id === parseInt(modelId));
                resolve({
                    success: !!model,
                    model: model || null,
                    error: !model ? "Không tìm thấy mô hình" : null
                });
            }, 400);
        });
    }
};

// Hàm để tạo dữ liệu ngẫu nhiên cho biểu đồ
function generateRandomData(min, max, count) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push(Math.random() * (max - min) + min);
    }
    return data;
}

// Hàm để tạo dữ liệu ngày tháng cho biểu đồ
function generateDateRange(days) {
    const dates = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(today.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
    }
    
    return dates;
}

// Hàm để tạo dữ liệu dự đoán theo ngày
function generatePredictionData(days) {
    const dates = generateDateRange(days);
    const counts = generateRandomData(30, 70, days).map(Math.floor);
    
    return dates.map((date, index) => ({
        date,
        count: counts[index]
    }));
}

// Cập nhật dữ liệu dự đoán hàng ngày
fakeModelStats.predictionUsage.daily_predictions = generatePredictionData(30);

// Tính tổng số lượt dự đoán
fakeModelStats.predictionUsage.total_predictions = fakeModelStats.predictionUsage.daily_predictions.reduce(
    (sum, item) => sum + item.count, 0
);