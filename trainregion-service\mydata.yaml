# YOLOv8 dataset configuration file
# This file defines the dataset structure for training YOLOv8 models

# Dataset paths
path: ./dataset  # Root directory of the dataset
train: images    # Training images directory (relative to 'path')
val: images      # Validation images directory (relative to 'path')

# Number of classes
nc: 1

# Class names
names:
  0: license_plate

# Optional: Dataset info
download: false  # Set to true if dataset needs to be downloaded
