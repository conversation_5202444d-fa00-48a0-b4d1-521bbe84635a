#!/usr/bin/env python3
"""
Migration script to update training_job status column to support 'stopped' status
"""

import mysql.connector
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """Update training_job status column to include 'stopped' status"""
    
    # Database connection config
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 13308)),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', 'password'),
        'database': os.getenv('DB_NAME', 'trainregion_db')
    }
    
    try:
        # Connect to database
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        logger.info("Connected to database")
        
        # Check current column definition
        cursor.execute("""
            SELECT COLUMN_TYPE 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = %s 
            AND TABLE_NAME = 'train_jobs' 
            AND COLUMN_NAME = 'status'
        """, (config['database'],))
        
        result = cursor.fetchone()
        if result:
            current_type = result[0]
            logger.info(f"Current status column type: {current_type}")
            
            # Check if 'stopped' is already in the enum
            if 'stopped' in current_type:
                logger.info("Status column already includes 'stopped'. No migration needed.")
                return
        
        # Update the column to include 'stopped'
        logger.info("Updating status column to include 'stopped'...")
        
        alter_query = """
            ALTER TABLE train_jobs 
            MODIFY COLUMN status ENUM('pending', 'running', 'completed', 'failed', 'stopped') 
            DEFAULT 'pending'
        """
        
        cursor.execute(alter_query)
        conn.commit()
        
        logger.info("Successfully updated status column")
        
        # Verify the change
        cursor.execute("""
            SELECT COLUMN_TYPE 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = %s 
            AND TABLE_NAME = 'train_jobs' 
            AND COLUMN_NAME = 'status'
        """, (config['database'],))
        
        result = cursor.fetchone()
        if result:
            logger.info(f"New status column type: {result[0]}")
        
    except mysql.connector.Error as e:
        logger.error(f"Database error: {e}")
        raise
    except Exception as e:
        logger.error(f"Migration error: {e}")
        raise
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        logger.info("Database connection closed")

if __name__ == "__main__":
    migrate_database()
