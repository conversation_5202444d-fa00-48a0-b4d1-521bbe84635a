# THIẾT KẾ TÁI CẤU TRÚC TRAINREGION SERVICE VÀ MODEL STATISTICS SERVICE

## 1. TỔNG QUAN THIẾT KẾ

### 1.1 Mục tiêu
- **TrainRegion Service**: Tập trung hoàn toàn vào việc training models với workflow đơn giản
- **Model Statistics Service**: Service riêng biệt để thống kê và phân tích models
- **Loại bỏ**: Bảng predict_logs và các tính năng không cần thiết

### 1.2 Workflow mới cho TrainRegion
```
1. Upload Dataset
→ 2. Cấu hình Training Parameters
→ 3. Click "Start Training"
→ 4. Monitor Progress + Stop Training (nếu cần)
→ 5. View Detailed Results
→ 6. Approve/Reject Model
```

## 2. THIẾT KẾ TRAINREGION SERVICE MỚI

### 2.1 Giao diện Training mới
```
┌─────────────────────────────────────────────────────────────┐
│                    TRAIN MODEL                             │
├─────────────────────────────────────────────────────────────┤
│  📁 Dataset: [Selected Dataset]                           │
│  ⚙️  Config: [Model Type] [Epochs] [Batch Size] [LR]      │
│  ┌─────────────────┐ ┌─────────────────┐                  │
│  │  START TRAINING │ │  STOP TRAINING  │ (disabled/enabled)│
│  └─────────────────┘ └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  📊 Training Progress (khi đang train)                    │
│  Progress: [████████░░] 80% - Epoch 80/100               │
│  Current mAP50: 0.892 | Best mAP50: 0.901                │
│  Time Elapsed: 45:30 | ETA: 11:20                        │
├─────────────────────────────────────────────────────────────┤
│  🎯 Training Results (sau khi hoàn thành)                 │
│  ┌─────────────────────────────────────────────────────┐  │
│  │ Model Name: License Plate Model v3                 │  │
│  │ Type: YOLOv8n | Epochs: 100 | Batch Size: 16       │  │
│  │ Training Time: 56:45                               │  │
│  │ Final mAP50: 0.901 | Final mAP95: 0.768           │  │
│  │ Dataset: LP_Dataset_2024 (1500 images)            │  │
│  │ Model Path: /models/lp_model_v3.pt                │  │
│  │ ┌─────────────┐ ┌─────────────┐                   │  │
│  │ │   APPROVE   │ │   REJECT    │                   │  │
│  │ └─────────────┘ └─────────────┘                   │  │
│  └─────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Database TrainRegion cập nhật
```sql
-- XÓA BẢNG predict_logs (không cần thiết)
DROP TABLE IF EXISTS predict_logs;

-- CẬP NHẬT bảng train_jobs thêm stop functionality
ALTER TABLE train_jobs ADD COLUMN can_stop BOOLEAN DEFAULT TRUE;
ALTER TABLE train_jobs ADD COLUMN stop_requested BOOLEAN DEFAULT FALSE;
ALTER TABLE train_jobs ADD COLUMN stopped_at TIMESTAMP NULL;

-- GIỮ NGUYÊN các bảng: models, train_jobs, datasets
```

### 2.3 API TrainRegion mới
```
POST /api/training/start - Bắt đầu training
POST /api/training/stop/{job_id} - Dừng training
GET /api/training/progress/{job_id} - Lấy progress real-time
GET /api/training/result/{job_id} - Lấy kết quả training chi tiết
WebSocket /training/progress - Real-time progress updates
```

### 2.4 Tính năng chính TrainRegion
- **Chỉ hiển thị model vừa train xong** (không hiển thị lịch sử)
- **Real-time progress monitoring** với WebSocket
- **Stop training functionality**
- **Detailed result display** sau khi hoàn thành
- **Approve/Reject workflow** cho model mới

## 3. THIẾT KẾ MODEL STATISTICS SERVICE

### 3.1 Thông tin Service
- **Port**: 3007
- **Database**: modelstats_db (file riêng)
- **Cấu trúc**: Theo pattern MVC như vehicle-service

### 3.2 Database Schema ModelStats (Đơn giản hóa)
```sql
-- CHỈ 1 BẢNG CHÍNH: training_history
CREATE TABLE training_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    model_id INT,
    model_name VARCHAR(255) NOT NULL,
    model_type ENUM('yolov8n', 'yolov8s', 'yolov8m', 'yolov8l', 'yolov8x') NOT NULL,
    dataset_name VARCHAR(255),
    dataset_size INT DEFAULT 0,
    epochs INT,
    batch_size INT,
    learning_rate FLOAT,
    training_time FLOAT,
    map50 FLOAT,
    map95 FLOAT,
    status ENUM('completed', 'failed', 'stopped') NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    model_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3 VIEWS để truy vấn thống kê:
-- v_model_stats_summary: Tổng quan (tổng models, tỉ lệ thành công, avg accuracy)
-- v_top_models: Top models hiệu suất cao nhất
-- v_model_type_stats: Thống kê theo loại model (yolov8n, yolov8s, etc.)
```

### 3.3 Giao diện Model Statistics (Đơn giản)
```
┌─────────────────────────────────────────────────────────────┐
│                    MODEL STATISTICS                        │
├─────────────────────────────────────────────────────────────┤
│  📊 Thống kê tổng quan                                     │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐          │
│  │ 25      │ │ 18      │ │ 7       │ │ 95.2%   │          │
│  │ Tổng    │ │ Hoàn    │ │ Thất    │ │ Tỉ lệ   │          │
│  │ Models  │ │ thành   │ │ bại     │ │ cao nhất│          │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘          │
├─────────────────────────────────────────────────────────────┤
│  🔍 Bộ lọc: [Loại Model] [Trạng thái] [Ngày tạo]         │
├─────────────────────────────────────────────────────────────┤
│  📋 Danh sách Models đã train                             │
│  ┌────┬──────────┬──────┬──────┬──────┬──────┬──────────┐  │
│  │ID  │Tên Model │Loại  │mAP50 │mAP95 │Status│Ngày tạo  │  │
│  ├────┼──────────┼──────┼──────┼──────┼──────┼──────────┤  │
│  │001 │LP-Model-1│YOLOv8│0.892 │0.756 │✅    │15/01/2024│  │
│  │002 │LP-Model-2│YOLOv8│0.901 │0.768 │✅    │20/01/2024│  │
│  └────┴──────────┴──────┴──────┴──────┴──────┴──────────┘  │
├─────────────────────────────────────────────────────────────┤
│  🏆 Top Models (hiệu suất cao nhất)                       │
│  [Bảng top 5 models với composite score cao nhất]         │
├─────────────────────────────────────────────────────────────┤
│  📈 Thống kê theo loại Model                              │
│  [Bảng thống kê: YOLOv8n, YOLOv8s, YOLOv8m, etc.]        │
└─────────────────────────────────────────────────────────────┘
```

## 4. KẾ HOẠCH THỰC HIỆN

### Bước 1: Cập nhật TrainRegion Database ✅
1. Backup database hiện tại
2. Xóa bảng `predict_logs`
3. Thêm trường stop cho `train_jobs`
4. Xóa các reference đến predict_logs

### Bước 2: Cập nhật TrainRegion Backend ✅
1. Xóa model PredictLog
2. Cập nhật training_controller thêm stop functionality
3. Thêm WebSocket support cho real-time progress
4. Cập nhật model_controller chỉ trả về model mới nhất

### Bước 3: Cập nhật TrainRegion Frontend ✅
1. Redesign ai-training.html với workflow mới
2. Thêm nút Start/Stop Training
3. Thêm real-time progress display
4. Thêm detailed result display sau khi train xong

### Bước 4: Tạo ModelStats Service ✅
1. Tạo cấu trúc service (port 3007)
2. Setup database modelstats_db
3. Tạo controllers và routes
4. Implement data collection từ trainregion

### Bước 5: Tạo ModelStats Frontend ✅
1. Tạo model-statistics.html
2. Dashboard với 3 bảng chính
3. Biểu đồ training trends
4. Model comparison features

### Bước 6: Integration ✅
1. Cập nhật docker-compose.yml
2. Setup data sync từ trainregion → modelstats
3. Test toàn bộ workflow
4. Cập nhật navigation

## 5. LỢI ÍCH CỦA THIẾT KẾ MỚI

### 5.1 TrainRegion Service
- **Đơn giản hóa**: Chỉ tập trung vào training
- **UX tốt hơn**: Real-time progress, stop functionality
- **Hiệu quả**: Không lưu trữ dữ liệu không cần thiết
- **Workflow rõ ràng**: Start → Monitor → Results → Approve

### 5.2 Model Statistics Service
- **Tách biệt trách nhiệm**: Analytics riêng biệt
- **Scalable**: Có thể mở rộng thêm tính năng
- **Comprehensive**: Toàn bộ thống kê ở một nơi
- **Historical data**: Lưu trữ lịch sử training từ nhiều nguồn

### 5.3 Hệ thống tổng thể
- **Microservices**: Mỗi service có trách nhiệm riêng
- **Maintainable**: Dễ bảo trì và phát triển
- **Flexible**: Có thể thay đổi từng service độc lập
- **User-friendly**: Giao diện phù hợp với từng mục đích sử dụng
