# GIẢI PHÁP AUTO-LABELING CHO TRAINREGION-SERVICE

## TỔNG QUAN

Khi user upload ảnh mà không có labels, chúng ta cần tạo labels tự động để có thể training được model hiệu quả. Dưới đây là 4 giải pháp ch<PERSON>h với phân tích chi tiết.

## GIẢI PHÁP 1: AUTO-LABELING VỚI PRE-TRAINED MODEL

### Nguyên lý
- Sử dụng model đã train sẵn (từ dataset-labelstudio) để predict trên ảnh mới
- Chuyển đổi predictions thành YOLO format labels
- Tạo dataset hoàn chỉnh để training

### Cách thực hiện
```python
def auto_label_with_pretrained(uploaded_images, dataset_id):
    # 1. Load model đã train tốt
    pretrained_model = YOLO("runs/detect/train2/weights/best.pt")

    # 2. <PERSON><PERSON><PERSON> <PERSON><PERSON> mục dataset
    dataset_path = f"uploads/dataset_{dataset_id}"
    images_path = os.path.join(dataset_path, 'images')
    labels_path = os.path.join(dataset_path, 'labels')

    # 3. Process từng ảnh
    for image_file in uploaded_images:
        # Lưu ảnh
        image_path = os.path.join(images_path, image_file.filename)
        image_file.save(image_path)

        # Predict để tạo label
        results = pretrained_model.predict(image_path, conf=0.3)

        # Convert sang YOLO format
        label_filename = os.path.splitext(image_file.filename)[0] + '.txt'
        label_path = os.path.join(labels_path, label_filename)

        with open(label_path, 'w') as f:
            for box in results[0].boxes:
                if box.conf[0] > 0.3:
                    # Tính toán YOLO coordinates
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    img_w, img_h = results[0].orig_shape[1], results[0].orig_shape[0]

                    x_center = (x1 + x2) / 2 / img_w
                    y_center = (y1 + y2) / 2 / img_h
                    width = (x2 - x1) / img_w
                    height = (y2 - y1) / img_h

                    f.write(f"0 {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")

    return dataset_path
```

### Ưu điểm
- ✅ **Độ chính xác cao**: 75-85% (dựa trên chất lượng model gốc)
- ✅ **Tự động hoàn toàn**: Không cần can thiệp thủ công
- ✅ **Nhanh chóng**: Xử lý hàng trăm ảnh trong vài phút
- ✅ **Sử dụng ngay**: Dataset có thể dùng training ngay lập tức
- ✅ **Học từ data thực**: Model gốc đã học từ license plate thật

### Nhược điểm
- ❌ **Phụ thuộc model gốc**: Chất lượng phụ thuộc vào model đã train
- ❌ **Bias inheritance**: Kế thừa bias từ model gốc
- ❌ **Không detect edge cases**: Khó với ảnh khác biệt nhiều so với training data
- ❌ **False positives**: Có thể tạo labels sai

### Khi nào sử dụng
- Có model base chất lượng cao
- Ảnh mới tương tự ảnh training gốc
- Cần tạo dataset nhanh cho production
- Chấp nhận độ chính xác 75-85%

## GIẢI PHÁP 2: HYBRID AUTO + MANUAL CORRECTION

### Nguyên lý
- Auto-label như Giải pháp 1
- Tạo interface để user review và sửa labels
- Cho phép approve/reject từng label

### Cách thực hiện
```python
def create_reviewable_dataset(uploaded_images, dataset_id):
    # Step 1: Auto-label
    auto_label_with_pretrained(uploaded_images, dataset_id)

    # Step 2: Tạo review data
    review_data = []
    dataset_path = f"uploads/dataset_{dataset_id}"

    for image_file in uploaded_images:
        image_path = f"{dataset_path}/images/{image_file.filename}"
        label_path = f"{dataset_path}/labels/{os.path.splitext(image_file.filename)[0]}.txt"

        # Load auto-generated labels
        predictions = []
        if os.path.exists(label_path):
            with open(label_path, 'r') as f:
                predictions = [line.strip() for line in f.readlines()]

        review_data.append({
            'image_id': len(review_data),
            'image_path': image_path,
            'predictions': predictions,
            'status': 'needs_review',
            'confidence': 'auto-generated'
        })

    # Step 3: Lưu review session
    review_session = {
        'dataset_id': dataset_id,
        'total_images': len(uploaded_images),
        'reviewed_count': 0,
        'approved_count': 0,
        'rejected_count': 0,
        'review_data': review_data,
        'status': 'in_review'
    }

    return review_session

# API cho review process
@app.route('/api/dataset/<int:dataset_id>/review/<int:image_id>', methods=['POST'])
def review_label(dataset_id, image_id):
    action = request.json.get('action')  # 'approve', 'reject', 'modify'
    new_labels = request.json.get('labels', [])

    if action == 'approve':
        # Giữ nguyên labels
        pass
    elif action == 'reject':
        # Xóa labels
        label_path = f"uploads/dataset_{dataset_id}/labels/image_{image_id}.txt"
        if os.path.exists(label_path):
            os.remove(label_path)
    elif action == 'modify':
        # Cập nhật labels mới
        label_path = f"uploads/dataset_{dataset_id}/labels/image_{image_id}.txt"
        with open(label_path, 'w') as f:
            for label in new_labels:
                f.write(f"{label}\n")

    return jsonify({'status': 'success'})
```

### Ưu điểm
- ✅ **Độ chính xác cao nhất**: 90-95% sau review
- ✅ **Kiểm soát chất lượng**: User có thể sửa lỗi
- ✅ **Học từ feedback**: Có thể cải thiện auto-labeling
- ✅ **Linh hoạt**: Có thể approve nhanh hoặc review kỹ
- ✅ **Traceability**: Biết label nào được sửa

### Nhược điểm
- ❌ **Tốn thời gian**: Cần review thủ công
- ❌ **Phức tạp**: Cần build review interface
- ❌ **Phụ thuộc user**: Chất lượng phụ thuộc vào người review
- ❌ **Không scale**: Khó với dataset lớn

### Khi nào sử dụng
- Cần độ chính xác cao nhất
- Dataset không quá lớn (< 500 ảnh)
- Có thời gian để review
- Chất lượng quan trọng hơn tốc độ

## GIẢI PHÁP 3: TEMPLATE-BASED LABELING

### Nguyên lý
- Sử dụng template cố định dựa trên pattern thông thường
- License plate thường ở vị trí predictable (giữa, dưới xe)
- Áp dụng bounding box với kích thước và vị trí chuẩn

### Cách thực hiện
```python
def apply_template_labels(uploaded_images, dataset_id, template_type='center'):
    templates = {
        'center': {
            'description': 'License plate ở giữa ảnh',
            'x_center': 0.5,    # Giữa theo chiều ngang
            'y_center': 0.6,    # Hơi thấp hơn giữa
            'width': 0.3,       # 30% chiều rộng ảnh
            'height': 0.15,     # 15% chiều cao ảnh
            'use_case': 'Ảnh chụp thẳng xe'
        },
        'bottom': {
            'description': 'License plate ở phía dưới',
            'x_center': 0.5,
            'y_center': 0.8,    # Gần đáy ảnh
            'width': 0.25,
            'height': 0.12,
            'use_case': 'Ảnh chụp từ xa'
        },
        'front_angle': {
            'description': 'License plate góc nghiêng',
            'x_center': 0.45,   # Hơi lệch trái
            'y_center': 0.65,
            'width': 0.35,
            'height': 0.18,
            'use_case': 'Ảnh chụp góc'
        }
    }

    template = templates.get(template_type, templates['center'])
    dataset_path = f"uploads/dataset_{dataset_id}"

    for image_file in uploaded_images:
        # Lưu ảnh
        image_path = os.path.join(dataset_path, 'images', image_file.filename)
        image_file.save(image_path)

        # Tạo label từ template
        label_filename = os.path.splitext(image_file.filename)[0] + '.txt'
        label_path = os.path.join(dataset_path, 'labels', label_filename)

        with open(label_path, 'w') as f:
            f.write(f"0 {template['x_center']:.6f} {template['y_center']:.6f} "
                   f"{template['width']:.6f} {template['height']:.6f}\n")

    return {
        'template_used': template_type,
        'template_info': template,
        'accuracy_estimate': 'medium'
    }

# API để chọn template
@app.route('/api/dataset/upload-with-template', methods=['POST'])
def upload_with_template():
    files = request.files.getlist('images')
    template_type = request.form.get('template_type', 'center')

    dataset_id = create_new_dataset()
    result = apply_template_labels(files, dataset_id, template_type)

    return jsonify(result)
```

### Ưu điểm
- ✅ **Cực nhanh**: Tạo labels trong vài giây
- ✅ **Đơn giản**: Không cần model phức tạp
- ✅ **Ổn định**: Kết quả consistent
- ✅ **Không phụ thuộc**: Không cần model pre-trained
- ✅ **Dễ customize**: Có thể tạo template mới

### Nhược điểm
- ❌ **Độ chính xác thấp**: 50-70%
- ❌ **Không linh hoạt**: Không adapt với ảnh khác biệt
- ❌ **Giả định mạnh**: Giả sử license plate ở vị trí cố định
- ❌ **Không học**: Không cải thiện theo thời gian

### Khi nào sử dụng
- Cần tạo dataset nhanh để test
- Ảnh có pattern rõ ràng
- Chấp nhận độ chính xác thấp
- Làm baseline để so sánh

## GIẢI PHÁP 4: PROGRESSIVE LEARNING

### Nguyên lý
- Training theo stages để cải thiện dần model
- Stage 1: Train với data chất lượng cao
- Stage 2: Fine-tune với auto-labeled data
- Stage 3: Active learning với hard examples

### Cách thực hiện
```python
def progressive_training_workflow():
    stages = {
        'stage_1': {
            'name': 'Base Model Training',
            'data': 'dataset-labelstudio (49 ảnh chất lượng cao)',
            'epochs': 100,
            'expected_map': 0.8,
            'purpose': 'Tạo foundation model'
        },
        'stage_2': {
            'name': 'Auto-labeled Expansion',
            'data': 'auto-labeled images từ user upload',
            'epochs': 50,
            'expected_map': 0.75,
            'purpose': 'Mở rộng dataset, improve generalization'
        },
        'stage_3': {
            'name': 'Active Learning Refinement',
            'data': 'hard examples được label thủ công',
            'epochs': 30,
            'expected_map': 0.85,
            'purpose': 'Cải thiện edge cases'
        }
    }
    return stages

def implement_progressive_training(user_images_dataset_id):
    # Stage 1: Train base model
    print("Stage 1: Training base model...")
    base_model = train_yolo_model(
        data_path="dataset-labelstudio/data.yaml",
        epochs=100,
        model_type="yolov8n"
    )

    # Stage 2: Auto-label user images
    print("Stage 2: Auto-labeling user images...")
    auto_label_with_model(base_model, user_images_dataset_id)

    # Combine datasets
    combined_dataset = combine_datasets([
        "dataset-labelstudio",
        f"uploads/dataset_{user_images_dataset_id}"
    ])

    # Fine-tune model
    finetuned_model = finetune_yolo_model(
        base_model=base_model,
        data_path=combined_dataset,
        epochs=50
    )

    # Stage 3: Active learning (optional)
    print("Stage 3: Identifying hard examples...")
    hard_examples = find_hard_examples(finetuned_model, user_images_dataset_id)

    return {
        'base_model': base_model,
        'finetuned_model': finetuned_model,
        'hard_examples': hard_examples,
        'training_stages': stages
    }

def find_hard_examples(model, dataset_id, confidence_threshold=0.5):
    """
    Tìm ảnh khó để label thủ công
    """
    hard_examples = []
    dataset_path = f"uploads/dataset_{dataset_id}"

    for image_file in os.listdir(f"{dataset_path}/images"):
        image_path = f"{dataset_path}/images/{image_file}"
        results = model.predict(image_path)

        # Ảnh khó = không detect được hoặc confidence thấp
        if len(results[0].boxes) == 0:
            hard_examples.append({
                'image': image_file,
                'reason': 'no_detection',
                'priority': 'high'
            })
        else:
            max_conf = max([box.conf[0].item() for box in results[0].boxes])
            if max_conf < confidence_threshold:
                hard_examples.append({
                    'image': image_file,
                    'reason': 'low_confidence',
                    'confidence': max_conf,
                    'priority': 'medium'
                })

    return hard_examples
```

### Ưu điểm
- ✅ **Chất lượng cao nhất**: Có thể đạt 85-90% mAP
- ✅ **Cải thiện liên tục**: Model ngày càng tốt
- ✅ **Tối ưu resource**: Focus vào ảnh khó
- ✅ **Scalable**: Có thể mở rộng với nhiều user
- ✅ **Production-ready**: Phù hợp cho hệ thống thực

### Nhược điểm
- ❌ **Phức tạp nhất**: Cần implement nhiều components
- ❌ **Tốn thời gian**: Training nhiều stages
- ❌ **Cần storage**: Lưu nhiều versions của model
- ❌ **Monitoring**: Cần theo dõi performance từng stage

### Khi nào sử dụng
- Hệ thống production lớn
- Có nhiều user upload data
- Cần chất lượng cao nhất
- Có resource để maintain

## SO SÁNH TỔNG QUAN

| Giải pháp | Độ chính xác | Thời gian setup | Complexity | Phù hợp cho |
|-----------|--------------|-----------------|------------|-------------|
| Auto-labeling | 75-85% | 5 phút | Trung bình | Production nhanh |
| Hybrid | 90-95% | 2-4 giờ | Cao | Chất lượng cao |
| Template | 50-70% | 1 phút | Thấp | Test/Demo |
| Progressive | 85-90% | 1-2 ngày | Rất cao | Enterprise |

## KHUYẾN NGHỊ IMPLEMENTATION

### Cho trainregion-service hiện tại:
1. **Bắt đầu với Auto-labeling** - cân bằng tốt
2. **Thêm Template** cho quick test
3. **Sau này mở rộng** sang Hybrid nếu cần

### Workflow đề xuất:
```python
@app.route('/api/dataset/upload-smart', methods=['POST'])
def smart_upload():
    method = request.form.get('method', 'auto')

    if method == 'auto':
        return auto_label_with_pretrained()
    elif method == 'template':
        return apply_template_labels()
    elif method == 'hybrid':
        return create_reviewable_dataset()
    else:
        return upload_for_manual_labeling()
```

## IMPLEMENTATION ROADMAP

### Phase 1: Auto-labeling cơ bản (1-2 ngày)
- [ ] Tạo `utils/auto_labeling.py`
- [ ] Implement auto-labeling với model hiện có
- [ ] Test với vài ảnh mẫu
- [ ] Tích hợp vào API upload

### Phase 2: Template system (0.5 ngày)
- [ ] Tạo template definitions
- [ ] Implement template application
- [ ] Thêm template selection UI

### Phase 3: Hybrid review (2-3 ngày)
- [ ] Tạo review interface
- [ ] Implement review API
- [ ] Test review workflow

### Phase 4: Progressive learning (1 tuần)
- [ ] Multi-stage training pipeline
- [ ] Hard example detection
- [ ] Model versioning

## CODE SAMPLES CHO TRAINREGION-SERVICE

### File: utils/auto_labeling.py
```python
import os
from ultralytics import YOLO
from PIL import Image

class AutoLabeler:
    def __init__(self, model_path="runs/detect/train2/weights/best.pt"):
        self.model = YOLO(model_path)
        self.confidence_threshold = 0.3

    def process_images(self, image_files, dataset_id, method='auto'):
        if method == 'auto':
            return self._auto_label(image_files, dataset_id)
        elif method == 'template':
            return self._template_label(image_files, dataset_id)
        else:
            return self._manual_label(image_files, dataset_id)

    def _auto_label(self, image_files, dataset_id):
        dataset_path = f"uploads/dataset_{dataset_id}"
        os.makedirs(f"{dataset_path}/images", exist_ok=True)
        os.makedirs(f"{dataset_path}/labels", exist_ok=True)

        labeled_count = 0
        for image_file in image_files:
            # Save image
            image_path = f"{dataset_path}/images/{image_file.filename}"
            image_file.save(image_path)

            # Generate label
            if self._create_auto_label(image_path, dataset_path, image_file.filename):
                labeled_count += 1

        self._create_classes_file(dataset_path)

        return {
            'dataset_id': dataset_id,
            'total_images': len(image_files),
            'labeled_images': labeled_count,
            'method': 'auto',
            'accuracy_estimate': 0.8
        }

    def _create_auto_label(self, image_path, dataset_path, filename):
        try:
            results = self.model.predict(image_path, conf=self.confidence_threshold)

            if len(results[0].boxes) == 0:
                return False

            label_filename = os.path.splitext(filename)[0] + '.txt'
            label_path = f"{dataset_path}/labels/{label_filename}"

            with open(label_path, 'w') as f:
                for box in results[0].boxes:
                    if box.conf[0] > self.confidence_threshold:
                        # Convert to YOLO format
                        x1, y1, x2, y2 = box.xyxy[0].tolist()
                        img_w, img_h = results[0].orig_shape[1], results[0].orig_shape[0]

                        x_center = (x1 + x2) / 2 / img_w
                        y_center = (y1 + y2) / 2 / img_h
                        width = (x2 - x1) / img_w
                        height = (y2 - y1) / img_h

                        f.write(f"0 {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")

            return True
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            return False
```

### File: controllers/dataset_controller.py (cập nhật)
```python
from utils.auto_labeling import AutoLabeler

class DatasetController:
    def __init__(self):
        self.auto_labeler = AutoLabeler()

    def upload_images_smart(self):
        try:
            files = request.files.getlist('images')
            method = request.form.get('method', 'auto')
            dataset_name = request.form.get('name', 'Untitled Dataset')

            # Create dataset record
            dataset = Dataset.create({
                'name': dataset_name,
                'upload_type': 'images',
                'status': 'processing'
            })

            # Process images
            result = self.auto_labeler.process_images(files, dataset.id, method)

            # Update dataset
            dataset.update({
                'image_count': result['total_images'],
                'has_labels': result['labeled_images'] > 0,
                'status': 'ready',
                'upload_path': f"uploads/dataset_{dataset.id}"
            })

            return jsonify({
                'success': True,
                'dataset': dataset.to_dict(),
                'labeling_result': result
            }), 201

        except Exception as e:
            return jsonify({'error': str(e)}), 500
```

## KẾT LUẬN VÀ KHUYẾN NGHỊ

### Cho trainregion-service hiện tại:
1. **Implement Auto-labeling trước** - ROI cao nhất
2. **Sử dụng model từ dataset-labelstudio** làm base
3. **Thêm confidence threshold setting** cho user
4. **Monitor accuracy** và cải thiện dần

### Success metrics:
- Auto-labeling accuracy > 75%
- Processing time < 30s/100 images
- User satisfaction với quality
- Reduced manual labeling effort

### Next steps:
1. Implement Auto-labeling basic
2. Test với real user data
3. Collect feedback và improve
4. Mở rộng sang các giải pháp khác
