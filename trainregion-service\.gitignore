# Python virtual environments
.venv/
venv/
ENV/
env/

# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg

# YOLOv8 specific
runs/
*.pt
# Không giữ lại file model pretrained vì quá lớn
# Người dùng cần tải xuống riêng

# Dataset files (tùy chọn, có thể bỏ comment nếu không muốn đẩy dataset lên)
# dataset/
# dataset-studio/

# Jupyter Notebook
.ipynb_checkpoints

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db

# Log files
*.log
logs/

# Temporary files
*.tmp
*.bak
*.backup

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.docker/

# Misc
.coverage
htmlcov/
.pytest_cache/
