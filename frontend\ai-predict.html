<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Prediction - <PERSON><PERSON> thống quản lý bi<PERSON>n số xe</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #fff;
            padding: 15px 20px;
        }
        .sidebar .nav-link:hover {
            background-color: #495057;
            color: #fff;
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: #fff;
        }
        .main-content {
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .result-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
        }
        .prediction-result {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .detection-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">Hệ thống quản lý</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="fas fa-tachometer-alt me-2"></i>Tổng quan
                            </a>
                        </li>
                        <li class="nav-item" id="add-vehicle-nav">
                            <a class="nav-link" href="add-vehicle.html">
                                <i class="fas fa-plus me-2"></i>Thêm xe mới
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="ai-training.html">
                                <i class="fas fa-brain me-2"></i>Huấn luyện AI
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="ai-predict.html">
                                <i class="fas fa-search me-2"></i>Dự đoán AI
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="model-stats.html">
                                <i class="fas fa-chart-bar me-2"></i>Thống kê mô hình
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4 px-3">
                        <div class="text-white-50 small">
                            <div id="user-info-sidebar"></div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dự đoán AI</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" onclick="refreshModels()">
                            <i class="fas fa-sync-alt me-1"></i>Làm mới
                        </button>
                    </div>
                </div>

                <div class="row">
                    <!-- Upload Section -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Upload Image</h5>
                            </div>
                            <div class="card-body">
                                <!-- Model Selection -->
                                <div class="mb-3">
                                    <label for="model-select" class="form-label">Select Model (Optional)</label>
                                    <select class="form-select" id="model-select">
                                        <option value="">Use latest model</option>
                                    </select>
                                </div>

                                <!-- Upload Area -->
                                <div class="upload-area" id="upload-area">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5>Drag & Drop Image Here</h5>
                                    <p class="text-muted">or click to browse</p>
                                    <input type="file" id="image-input" accept="image/*" style="display: none;">
                                </div>

                                <!-- Image Preview -->
                                <div id="image-preview-container" style="display: none;">
                                    <img id="image-preview" class="image-preview" alt="Preview">
                                    <div class="text-center mt-3">
                                        <button type="button" class="btn btn-success" id="predict-btn">
                                            <i class="fas fa-search me-1"></i>Predict
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="clearImage()">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Prediction Results</h5>
                            </div>
                            <div class="card-body">
                                <div id="results-container">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-image fa-3x mb-3"></i>
                                        <p>Upload an image to see prediction results</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Prediction History -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Prediction History</h5>
                    </div>
                    <div class="card-body">
                        <div id="history-container">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script>
        // Check authentication
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user) {
            window.location.href = 'login.html';
        }

        // Update UI based on user role
        document.getElementById("user-info-sidebar").innerHTML = `
            <strong>${user.fullName}</strong><br>
            <small>${user.role === "admin" ? "Quản trị viên" : "Cảnh sát"}</small>
        `;

        // Only show Add Vehicle button for admin
        if (user.role !== "admin") {
            document.getElementById("add-vehicle-nav").style.display = "none";
        }

        let selectedFile = null;

        // Logout function
        function logout() {
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            window.location.href = 'login.html';
        }

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadModels();
            loadPredictHistory();
            setupUploadArea();
        });

        // Setup upload area
        function setupUploadArea() {
            const uploadArea = document.getElementById('upload-area');
            const imageInput = document.getElementById('image-input');

            uploadArea.addEventListener('click', () => imageInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            imageInput.addEventListener('change', handleFileSelect);
            document.getElementById('predict-btn').addEventListener('click', predictImage);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file');
                return;
            }

            selectedFile = file;
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('image-preview').src = e.target.result;
                document.getElementById('image-preview-container').style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        function clearImage() {
            selectedFile = null;
            document.getElementById('image-preview-container').style.display = 'none';
            document.getElementById('image-input').value = '';
            document.getElementById('results-container').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-image fa-3x mb-3"></i>
                    <p>Upload an image to see prediction results</p>
                </div>
            `;
        }

        // Load models for selection
        async function loadModels() {
            try {
                const response = await API.trainRegion.getAllModels();
                const modelSelect = document.getElementById('model-select');

                response.data.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = `${model.name} (${model.type})`;
                    modelSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading models:', error);
            }
        }

        // Predict image
        async function predictImage() {
            if (!selectedFile) {
                alert('Please select an image first');
                return;
            }

            console.log('[FRONTEND] Starting prediction...');
            console.log('[FRONTEND] File:', selectedFile.name, 'Size:', selectedFile.size);

            const predictBtn = document.getElementById('predict-btn');
            const originalText = predictBtn.innerHTML;
            predictBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Predicting...';
            predictBtn.disabled = true;

            try {
                const modelId = document.getElementById('model-select').value || null;
                console.log('[FRONTEND] Model ID:', modelId || 'Latest');

                console.log('[FRONTEND] Sending request to API...');
                const response = await API.trainRegion.predictImage(selectedFile, modelId);

                console.log('[FRONTEND] Response received:', response);
                displayPredictionResults(response);
                loadPredictHistory(); // Refresh history
            } catch (error) {
                console.error('[FRONTEND] Prediction error:', error);
                alert('Error predicting image: ' + error.message);
            } finally {
                predictBtn.innerHTML = originalText;
                predictBtn.disabled = false;
                console.log('[FRONTEND] Prediction process completed');
            }
        }

        // Display prediction results
        function displayPredictionResults(result) {
            const container = document.getElementById('results-container');

            let html = `
                <div class="prediction-result">
                    <h6>Prediction Results</h6>
                    <p><strong>Model:</strong> ${result.model_name}</p>
                    <p><strong>Detections:</strong> ${result.num_detections}</p>
                    <p><strong>Processing Time:</strong> ${result.processing_time.toFixed(3)}s</p>

                    ${result.result_image ? `
                        <div class="mt-3">
                            <h6>Result Image:</h6>
                            <img src="data:image/jpeg;base64,${result.result_image}" class="result-image" alt="Result">
                        </div>
                    ` : ''}

                    ${result.detections.length > 0 ? `
                        <div class="mt-3">
                            <h6>Detections:</h6>
                            ${result.detections.map(detection => `
                                <div class="detection-item">
                                    <strong>${detection.class_name}</strong>
                                    (Confidence: ${(detection.confidence * 100).toFixed(1)}%)
                                    <br>
                                    <small>Bbox: [${detection.bbox.map(x => x.toFixed(1)).join(', ')}]</small>
                                </div>
                            `).join('')}
                        </div>
                    ` : '<p class="text-muted mt-3">No detections found</p>'}
                </div>
            `;

            container.innerHTML = html;
        }

        // Load prediction history
        async function loadPredictHistory() {
            try {
                const response = await API.trainRegion.getPredictHistory();
                displayPredictHistory(response.data);
            } catch (error) {
                document.getElementById('history-container').innerHTML =
                    '<div class="alert alert-danger">Error loading prediction history: ' + error.message + '</div>';
            }
        }

        // Display prediction history
        function displayPredictHistory(history) {
            const container = document.getElementById('history-container');

            if (history.length === 0) {
                container.innerHTML = '<p class="text-muted">No prediction history found.</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
            html += '<th>ID</th><th>Model</th><th>Detections</th><th>Processing Time</th><th>Date</th>';
            html += '</tr></thead><tbody>';

            history.forEach(item => {
                html += `<tr>
                    <td>${item.id}</td>
                    <td>${item.model_name}</td>
                    <td>${item.num_detections}</td>
                    <td>${item.processing_time ? item.processing_time.toFixed(3) + 's' : 'N/A'}</td>
                    <td>${new Date(item.created_at).toLocaleString()}</td>
                </tr>`;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }
    </script>
</body>
</html>


