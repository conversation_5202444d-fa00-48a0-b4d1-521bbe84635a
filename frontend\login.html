<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Đ<PERSON>ng nhập - <PERSON><PERSON> thống quản lý biển số xe</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      body {
        background-color: #f8f9fa;
      }
      .auth-container {
        max-width: 400px;
        margin: 100px auto;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        background-color: white;
      }
      .logo {
        text-align: center;
        margin-bottom: 20px;
      }
      .logo h1 {
        color: #0d6efd;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="auth-container">
      <div class="logo">
        <h1><PERSON><PERSON> thống quản lý biển số xe</h1>
      </div>
      <h2 class="text-center mb-4"><PERSON><PERSON><PERSON> nhập</h2>
      <div
        class="alert alert-danger"
        id="login-error"
        style="display: none"
      ></div>

      <form id="login-form">
        <div class="mb-3">
          <label for="username" class="form-label">Tên đăng nhập</label>
          <input type="text" class="form-control" id="username" required />
        </div>
        <div class="mb-3">
          <label for="password" class="form-label">Mật khẩu</label>
          <input type="password" class="form-control" id="password" required />
        </div>
        <div class="d-grid">
          <button type="submit" class="btn btn-primary">Đăng nhập</button>
        </div>
      </form>

      <div class="mt-3 text-center">
        <p class="mb-0">Tài khoản demo:</p>
        <p class="mb-0">Admin: username: admin, password: admin123</p>
        <p class="mb-0">Police: username: police, password: police123</p>
      </div>
    </div>

    <script src="js/api.js"></script>
    <script>
      document
        .getElementById("login-form")
        .addEventListener("submit", async function (e) {
          e.preventDefault();

          const username = document.getElementById("username").value;
          const password = document.getElementById("password").value;
          const loginError = document.getElementById("login-error");

          loginError.style.display = "none";

          try {
            // Gọi API đăng nhập
            const response = await API.auth.login(username, password);

            // Lưu thông tin người dùng vào localStorage
            localStorage.setItem("user", JSON.stringify(response.user));
            localStorage.setItem("token", response.token);

            // Chuyển hướng đến trang dashboard
            window.location.href = "dashboard.html";
          } catch (error) {
            // Hiển thị thông báo lỗi
            loginError.textContent =
              error.message ||
              "Tên đăng nhập hoặc mật khẩu không đúng. Vui lòng thử lại.";
            loginError.style.display = "block";
          }
        });
    </script>
  </body>
</html>
