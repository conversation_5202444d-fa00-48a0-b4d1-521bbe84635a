<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thống kê mô hình - <PERSON>ệ thống quản lý biển số xe</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #fff;
            padding: 15px 20px;
        }
        .sidebar .nav-link:hover {
            background-color: #495057;
            color: #fff;
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: #fff;
        }
        .main-content {
            padding: 20px;
        }
        .stats-card {
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            height: 100%;
        }
        .stats-card .card-header {
            font-weight: 600;
            background-color: #f8f9fa;
        }
        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .model-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">Hệ thống quản lý</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="fas fa-tachometer-alt me-2"></i>Tổng quan
                            </a>
                        </li>
                        <li class="nav-item" id="add-vehicle-nav">
                            <a class="nav-link" href="add-vehicle.html">
                                <i class="fas fa-plus me-2"></i>Thêm xe mới
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="ai-training.html">
                                <i class="fas fa-brain me-2"></i>Huấn luyện AI
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="ai-predict.html">
                                <i class="fas fa-search me-2"></i>Dự đoán AI
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="model-stats.html">
                                <i class="fas fa-chart-bar me-2"></i>Thống kê mô hình
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4 px-3">
                        <div class="text-white-50 small">
                            <div id="user-info-sidebar"></div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Thống kê mô hình học</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportReport()">
                                <i class="fas fa-download me-1"></i>Xuất báo cáo
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                <i class="fas fa-sync me-1"></i>Làm mới
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-calendar me-1"></i>30 ngày qua
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="timeRangeDropdown">
                                <li><a class="dropdown-item" href="#">7 ngày qua</a></li>
                                <li><a class="dropdown-item" href="#">30 ngày qua</a></li>
                                <li><a class="dropdown-item" href="#">3 tháng qua</a></li>
                                <li><a class="dropdown-item" href="#">6 tháng qua</a></li>
                                <li><a class="dropdown-item" href="#">1 năm qua</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Tổng quan -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-primary">24</div>
                                <div class="metric-label">Tổng số mô hình</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-success">18</div>
                                <div class="metric-label">Mô hình đã phê duyệt</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-info">0.87</div>
                                <div class="metric-label">mAP50 trung bình</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Biểu đồ hiệu suất mô hình -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card stats-card">
                            <div class="card-header">
                                Hiệu suất mô hình theo loại
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="modelPerformanceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card stats-card">
                            <div class="card-header">
                                Thời gian training trung bình
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="trainingTimeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Xu hướng training -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card stats-card">
                            <div class="card-header">
                                Xu hướng training theo thời gian
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="trainingTrendsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-header">
                                Tỷ lệ thành công/thất bại
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="successRateChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mô hình có hiệu suất tốt nhất -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card stats-card">
                            <div class="card-header">
                                Mô hình có hiệu suất tốt nhất
                            </div>
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-4 text-center">
                                        <div class="display-4 text-success">0.92</div>
                                        <div class="text-muted">mAP50</div>
                                    </div>
                                    <div class="col-md-8">
                                        <h5>license_plate_detector_v3</h5>
                                        <p class="mb-1"><strong>Loại:</strong> YOLOv5l</p>
                                        <p class="mb-1"><strong>Thời gian training:</strong> 6.8 giờ</p>
                                        <p class="mb-1"><strong>Dataset:</strong> Vietnam License Plates 2023</p>
                                        <p class="mb-1"><strong>Ngày tạo:</strong> 15/09/2023</p>
                                        <p class="mb-0"><strong>Trạng thái:</strong> <span class="badge bg-success">Đã phê duyệt</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/model-stats-data.js"></script>
    <script src="js/model-stats.js"></script>
    <script>
        // Sửa phần kiểm tra xác thực để tránh lỗi JSON
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            let user = null;
            try {
                const userData = localStorage.getItem('user');
                if (userData) {
                    user = JSON.parse(userData);
                }
            } catch (error) {
                console.error("Error parsing user data:", error);
            }
            
            if (!user) {
                // Nếu không có thông tin người dùng, tạo một người dùng mặc định cho demo
                user = {
                    fullName: "Người dùng Demo",
                    role: "admin"
                };
                // Lưu vào localStorage để tránh lỗi lần sau
                localStorage.setItem('user', JSON.stringify(user));
            }

            // Update UI based on user role
            const userInfoSidebar = document.getElementById("user-info-sidebar");
            if (userInfoSidebar) {
                userInfoSidebar.innerHTML = `
                    <strong>${user.fullName}</strong><br>
                    <small>${user.role === "admin" ? "Quản trị viên" : "Cảnh sát"}</small>
                `;
            }

            // Only show Add Vehicle button for admin
            const addVehicleNav = document.getElementById("add-vehicle-nav");
            if (addVehicleNav && user.role !== "admin") {
                addVehicleNav.style.display = "none";
            }
        });

        // Hàm đăng xuất
        function logout() {
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            window.location.href = 'login.html';
        }
    </script>
</body>
</html>



