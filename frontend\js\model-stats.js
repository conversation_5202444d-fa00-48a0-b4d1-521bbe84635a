// Hàm để lấy dữ liệu thống kê từ API
async function fetchModelStats() {
    try {
        const response = await API.stats.getModelPerformanceStats();
        if (response.success) {
            updateModelPerformanceUI(response.stats);
        } else {
            console.error("Failed to fetch model stats:", response.error);
            showAlert("Không thể tải dữ liệu thống kê mô hình", "danger");
        }
    } catch (error) {
        console.error("Error fetching model stats:", error);
        showAlert("Lỗi khi tải dữ liệu thống kê", "danger");
    }
}

// Hàm để lấy dữ liệu xu hướng training
async function fetchTrainingTrends() {
    try {
        const response = await API.stats.getTrainingTrends();
        if (response.success) {
            updateTrainingTrendsUI(response.trends);
        } else {
            console.error("Failed to fetch training trends:", response.error);
        }
    } catch (error) {
        console.error("Error fetching training trends:", error);
    }
}

// Cập nhật UI với dữ liệu hiệu suất mô hình
function updateModelPerformanceUI(stats) {
    // Cập nhật các số liệu tổng quan
    const totalModelsElement = document.querySelector('.metric-value.text-primary');
    if (totalModelsElement) {
        totalModelsElement.textContent = stats.model_type_stats.reduce((sum, item) => sum + item.count, 0);
    }
    
    const approvedModelsElement = document.querySelector('.metric-value.text-success');
    if (approvedModelsElement) {
        approvedModelsElement.textContent = stats.approval_stats.find(item => item.is_approved)?.count || 0;
    }
    
    // Tính mAP50 trung bình
    const totalModels = stats.model_type_stats.reduce((sum, item) => sum + item.count, 0);
    const weightedMapSum = stats.model_type_stats.reduce((sum, item) => sum + (item.avg_map50 * item.count), 0);
    const avgMap50 = totalModels > 0 ? (weightedMapSum / totalModels).toFixed(2) : "0.00";
    
    const avgMapElement = document.querySelector('.metric-value.text-info');
    if (avgMapElement) {
        avgMapElement.textContent = avgMap50;
    }
    
    // Cập nhật biểu đồ hiệu suất mô hình
    const modelTypes = stats.model_type_stats.map(item => item.type);
    const map50Values = stats.model_type_stats.map(item => item.avg_map50);
    const map95Values = stats.model_type_stats.map(item => item.avg_map95);
    
    updateModelPerformanceChart(modelTypes, map50Values, map95Values);
    
    // Cập nhật biểu đồ thời gian training
    const trainingTimes = stats.model_type_stats.map(item => item.avg_training_time);
    updateTrainingTimeChart(modelTypes, trainingTimes);
    
    // Cập nhật thông tin mô hình tốt nhất
    if (stats.best_model) {
        updateBestModelInfo(stats.best_model);
    }
}

// Cập nhật UI với dữ liệu xu hướng training
function updateTrainingTrendsUI(trends) {
    // Cập nhật biểu đồ xu hướng training
    const months = trends.monthly_jobs.map(item => item.month);
    const jobCounts = trends.monthly_jobs.map(item => item.job_count);
    const mapValues = trends.map_trends.map(item => item.avg_map50);
    
    updateTrainingTrendsChart(months, jobCounts, mapValues);
    
    // Cập nhật biểu đồ tỷ lệ thành công/thất bại
    const successCount = trends.status_trends.filter(item => item.status === 'completed').reduce((sum, item) => sum + item.count, 0);
    const failedCount = trends.status_trends.filter(item => item.status === 'failed').reduce((sum, item) => sum + item.count, 0);
    const pendingCount = trends.status_trends.filter(item => item.status === 'pending' || item.status === 'running').reduce((sum, item) => sum + item.count, 0);
    const stoppedCount = trends.status_trends.filter(item => item.status === 'stopped').reduce((sum, item) => sum + item.count, 0);
    
    updateSuccessRateChart(successCount, failedCount, pendingCount, stoppedCount);
}

// Cập nhật biểu đồ hiệu suất mô hình
function updateModelPerformanceChart(modelTypes, map50Values, map95Values) {
    const canvas = document.getElementById('modelPerformanceChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Nếu biểu đồ đã tồn tại, hủy nó
    if (window.modelPerformanceChart instanceof Chart) {
        window.modelPerformanceChart.destroy();
    }
    
    window.modelPerformanceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: modelTypes,
            datasets: [
                {
                    label: 'mAP50',
                    data: map50Values,
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                },
                {
                    label: 'mAP95',
                    data: map95Values,
                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1
                }
            }
        }
    });
}

// Cập nhật biểu đồ thời gian training
function updateTrainingTimeChart(modelTypes, trainingTimes) {
    const canvas = document.getElementById('trainingTimeChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Nếu biểu đồ đã tồn tại, hủy nó
    if (window.trainingTimeChart instanceof Chart) {
        window.trainingTimeChart.destroy();
    }
    
    window.trainingTimeChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: modelTypes,
            datasets: [{
                label: 'Thời gian (giờ)',
                data: trainingTimes,
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Cập nhật biểu đồ xu hướng training
function updateTrainingTrendsChart(months, jobCounts, mapValues) {
    const canvas = document.getElementById('trainingTrendsChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Nếu biểu đồ đã tồn tại, hủy nó
    if (window.trainingTrendsChart instanceof Chart) {
        window.trainingTrendsChart.destroy();
    }
    
    window.trainingTrendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Số lượng training jobs',
                data: jobCounts,
                fill: false,
                borderColor: 'rgba(54, 162, 235, 1)',
                tension: 0.1
            },
            {
                label: 'mAP50 trung bình',
                data: mapValues,
                fill: false,
                borderColor: 'rgba(255, 99, 132, 1)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Số lượng jobs'
                    }
                },
                y1: {
                    beginAtZero: true,
                    max: 1,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false
                    },
                    title: {
                        display: true,
                        text: 'mAP50'
                    }
                }
            }
        }
    });
}

// Cập nhật biểu đồ tỷ lệ thành công/thất bại
function updateSuccessRateChart(successCount, failedCount, pendingCount, stoppedCount) {
    const canvas = document.getElementById('successRateChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Nếu biểu đồ đã tồn tại, hủy nó
    if (window.successRateChart instanceof Chart) {
        window.successRateChart.destroy();
    }
    
    window.successRateChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Thành công', 'Thất bại', 'Đang xử lý', 'Đã dừng'],
            datasets: [{
                data: [successCount, failedCount, pendingCount, stoppedCount],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.5)',
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(255, 205, 86, 0.5)',
                    'rgba(153, 102, 255, 0.5)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(255, 205, 86, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Cập nhật bảng mô hình sử dụng nhiều nhất
function updateMostUsedModelsTable(models) {
    const tableBody = document.getElementById('best-models-table');
    
    if (!tableBody || !models || models.length === 0) return;
    
    let tableHTML = '';
    
    models.forEach(model => {
        tableHTML += `
            <tr>
                <td>${model.name}</td>
                <td><span class="badge bg-primary model-badge">${model.type || 'Unknown'}</span></td>
                <td>${model.map50 ? model.map50.toFixed(2) : 'N/A'}</td>
                <td>${model.map95 ? model.map95.toFixed(2) : 'N/A'}</td>
                <td>${model.training_time ? (model.training_time / 60).toFixed(1) + ' giờ' : 'N/A'}</td>
            </tr>
        `;
    });
    
    tableBody.innerHTML = tableHTML;
}

// Cập nhật thông tin mô hình tốt nhất
function updateBestModelInfo(model) {
    const bestModelCard = findElementByHeaderText("Mô hình có hiệu suất tốt nhất");
    
    if (!bestModelCard || !model) return;
    
    const infoContainer = bestModelCard.querySelector('.card-body');
    if (!infoContainer) return;
    
    infoContainer.innerHTML = `
        <div class="row align-items-center">
            <div class="col-md-4 text-center">
                <div class="display-4 text-success">${model.map50 ? model.map50.toFixed(2) : 'N/A'}</div>
                <div class="text-muted">mAP50</div>
            </div>
            <div class="col-md-8">
                <h5>${model.name}</h5>
                <p class="mb-1"><strong>Loại:</strong> ${model.type || 'Unknown'}</p>
                <p class="mb-1"><strong>Thời gian training:</strong> ${model.training_time ? (model.training_time / 60).toFixed(1) + ' giờ' : 'N/A'}</p>
                <p class="mb-1"><strong>Dataset:</strong> ${model.dataset_name || 'N/A'}</p>
                <p class="mb-1"><strong>Ngày tạo:</strong> ${model.created_at ? new Date(model.created_at).toLocaleDateString('vi-VN') : 'N/A'}</p>
                <p class="mb-0"><strong>Trạng thái:</strong> <span class="badge bg-success">Đã phê duyệt</span></p>
            </div>
        </div>
    `;
}

// Hiển thị thông báo
function showAlert(message, type = 'info') {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
    alertContainer.setAttribute('role', 'alert');
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // Tự động ẩn sau 5 giây
    setTimeout(() => {
        alertContainer.classList.remove('show');
        setTimeout(() => alertContainer.remove(), 300);
    }, 5000);
}

// Hàm xuất báo cáo
function exportReport() {
    // Giả lập xuất báo cáo
    showAlert('Đang xuất báo cáo...', 'info');
    
    setTimeout(() => {
        showAlert('Báo cáo đã được xuất thành công!', 'success');
    }, 2000);
}

// Khởi tạo trang
document.addEventListener('DOMContentLoaded', function() {
    // Lấy dữ liệu từ API
    fetchModelStats();
    fetchTrainingTrends();
    
    // Xử lý sự kiện làm mới
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            fetchModelStats();
            fetchTrainingTrends();
            showAlert('Dữ liệu đã được làm mới', 'success');
        });
    }
    
    // Xử lý sự kiện thay đổi khoảng thời gian
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const timeRange = this.textContent;
            const dropdownButton = document.getElementById('timeRangeDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="fas fa-calendar me-1"></i>${timeRange}`;
            }
            // Cập nhật dữ liệu theo khoảng thời gian mới
            fetchDataByTimeRange(timeRange);
        });
    });
});

// Hàm để lấy dữ liệu theo khoảng thời gian
function fetchDataByTimeRange(timeRange) {
    // Giả lập việc lấy dữ liệu theo khoảng thời gian
    showAlert(`Đang tải dữ liệu cho ${timeRange}...`, 'info');
    
    setTimeout(() => {
        fetchModelStats();
        fetchTrainingTrends();
    }, 500);
}

// Helper function để tìm element bằng text trong header
function findElementByHeaderText(headerText) {
    const headers = document.querySelectorAll('.card-header');
    for (let header of headers) {
        if (header.textContent.trim().includes(headerText)) {
            return header.closest('.card');
        }
    }
    return null;
}



