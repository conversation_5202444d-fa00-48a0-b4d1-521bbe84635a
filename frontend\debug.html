<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Debug API Connections</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Login</h3>
                <button class="btn btn-primary" onclick="testLogin()">Test Login</button>
                <div id="login-result" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Vehicle API</h3>
                <button class="btn btn-success" onclick="testVehicles()">Test Get Vehicles</button>
                <div id="vehicle-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Console Logs</h3>
                <div id="console-logs" class="border p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;"></div>
            </div>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script>
        // Override console.log to display in page
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('console-logs');
        
        function addLog(message, type = 'log') {
            const div = document.createElement('div');
            div.className = type === 'error' ? 'text-danger' : 'text-dark';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(div);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        async function testLogin() {
            try {
                console.log('Testing login...');
                const result = await API.auth.login("admin", "admin123");
                console.log('Login result:', JSON.stringify(result, null, 2));
                
                if (result.token && result.user) {
                    localStorage.setItem("user", JSON.stringify(result.user));
                    localStorage.setItem("token", result.token);
                    
                    document.getElementById("login-result").innerHTML = `
                        <div class="alert alert-success">
                            <strong>Login Success!</strong><br>
                            User: ${result.user.fullName}<br>
                            Role: ${result.user.role}<br>
                            Token: ${result.token.substring(0, 20)}...
                        </div>
                    `;
                } else {
                    document.getElementById("login-result").innerHTML = `
                        <div class="alert alert-warning">
                            <strong>Login Response:</strong><br>
                            ${JSON.stringify(result, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById("login-result").innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Login Failed!</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testVehicles() {
            try {
                console.log('Testing vehicle API...');
                console.log('Current token:', localStorage.getItem("token"));
                
                const vehicles = await API.vehicle.getAllVehicles();
                console.log('Vehicles result:', vehicles);
                
                if (Array.isArray(vehicles)) {
                    document.getElementById("vehicle-result").innerHTML = `
                        <div class="alert alert-success">
                            <strong>Vehicles Loaded!</strong><br>
                            Count: ${vehicles.length} vehicles<br>
                            First vehicle: ${vehicles[0] ? vehicles[0].licensePlate : 'None'}
                        </div>
                    `;
                } else {
                    document.getElementById("vehicle-result").innerHTML = `
                        <div class="alert alert-warning">
                            <strong>Unexpected Response:</strong><br>
                            ${JSON.stringify(vehicles, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Vehicle API error:', error);
                document.getElementById("vehicle-result").innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Vehicle API Failed!</strong><br>
                        Error: ${error.message}<br>
                        Stack: ${error.stack}
                    </div>
                `;
            }
        }
        
        // Auto test on page load
        window.onload = function() {
            console.log('Debug page loaded');
            console.log('API_URL:', 'http://localhost:3000');
            
            // Check if already logged in
            const user = localStorage.getItem("user");
            const token = localStorage.getItem("token");
            
            if (user && token) {
                console.log('Already logged in:', user);
                testVehicles();
            } else {
                console.log('Not logged in, testing login first...');
                testLogin();
            }
        };
    </script>
</body>
</html>
