// API endpoints
const API_URL = "http://localhost:3000";

// API object
const API = {
    auth: {
        login: async (username, password) => {
            try {
                const response = await fetch(`${API_URL}/api/auth/login`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ username, password }),
                });
                return await response.json();
            } catch (error) {
                console.error("Login error:", error);
                return { success: false, message: "Lỗi kết nối đến máy chủ" };
            }
        },
        verify: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/auth/verify`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Verify error:", error);
                return { success: false };
            }
        },
        getProfile: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/auth/me`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get profile error:", error);
                return { success: false };
            }
        }
    },
    vehicle: {
        getAllVehicles: async () => {
            try {
                console.log("getAllVehicles called");
                const token = localStorage.getItem("token");
                console.log("Token:", token ? token.substring(0, 20) + "..." : "No token");

                if (!token) {
                    console.error("No token found");
                    return { success: false };
                }

                console.log("Making API call to:", `${API_URL}/api/vehicles`);
                const response = await fetch(`${API_URL}/api/vehicles`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                console.log("Response status:", response.status);
                console.log("Response ok:", response.ok);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error("API error response:", errorText);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const data = await response.json();
                console.log("API response data:", data);
                console.log("Data type:", typeof data);
                console.log("Is array:", Array.isArray(data));

                return data;
            } catch (error) {
                console.error("Get all vehicles error:", error);
                console.error("Error details:", error.message);
                throw error;
            }
        },
        getVehicleById: async (id) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Get vehicle by ID error:", error);
                throw error;
            }
        },
        getVehicleByLicensePlate: async (licensePlate) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/plate/${licensePlate}`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Get vehicle by license plate error:", error);
                throw error;
            }
        },
        createVehicle: async (vehicleData) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "x-auth-token": token,
                    },
                    body: JSON.stringify(vehicleData),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Create vehicle error:", error);
                throw error;
            }
        },
        updateVehicle: async (id, vehicleData) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json",
                        "x-auth-token": token,
                    },
                    body: JSON.stringify(vehicleData),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Update vehicle error:", error);
                throw error;
            }
        },
        deleteVehicle: async (id) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                    method: "DELETE",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Delete vehicle error:", error);
                throw error;
            }
        }
    },
    training: {
        // ... các API liên quan đến training
    },
    prediction: {
        // ... các API liên quan đến prediction
    },
    // Thêm API cho thống kê mô hình
    stats: {
        getModelPerformanceStats: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/model-performance`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get model stats error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        },
        getTrainingTrends: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/training-trends`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get training trends error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        },
        getPredictionUsage: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/prediction-usage`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get prediction usage error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        }
    }
};

// For use in browser
window.API = API;

