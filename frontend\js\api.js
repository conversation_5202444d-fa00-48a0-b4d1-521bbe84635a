// API endpoints
const API_URL = "http://localhost:3000";

// API object
const API = {
    auth: {
        login: async (username, password) => {
            try {
                // Thử kết nối API trước
                try {
                    const response = await fetch(`${API_URL}/api/auth/login`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ username, password }),
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (apiError) {
                    console.log("Auth API not available, using mock data");
                }

                // Nếu API không khả dụng, sử dụng mock authentication
                if (username === "admin" && password === "admin123") {
                    return {
                        success: true,
                        token: "mock-admin-token",
                        user: {
                            id: 1,
                            username: "admin",
                            fullName: "Quản trị viên",
                            role: "admin"
                        }
                    };
                } else if (username === "police" && password === "police123") {
                    return {
                        success: true,
                        token: "mock-police-token",
                        user: {
                            id: 2,
                            username: "police",
                            fullName: "Cảnh sát giao thông",
                            role: "police"
                        }
                    };
                } else {
                    throw new Error("Tên đăng nhập hoặc mật khẩu không đúng");
                }
            } catch (error) {
                console.error("Login error:", error);
                throw error;
            }
        },
        verify: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/auth/verify`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Verify error:", error);
                return { success: false };
            }
        },
        getProfile: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/auth/me`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get profile error:", error);
                return { success: false };
            }
        }
    },
    vehicle: {
        getAllVehicles: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                // Thử kết nối API trước
                try {
                    const response = await fetch(`${API_URL}/api/vehicles`, {
                        method: "GET",
                        headers: {
                            "x-auth-token": token,
                        },
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (apiError) {
                    console.log("API not available, using mock data");
                }

                // Nếu API không khả dụng, trả về dữ liệu mock
                return [
                    {
                        id: 1,
                        licensePlate: "29A-12345",
                        ownerName: "Nguyễn Văn A",
                        ownerID: "001234567890",
                        vehicleType: "Sedan",
                        brand: "Toyota",
                        model: "Camry",
                        color: "Đen",
                        registrationDate: "2022-01-15",
                        expiryDate: "2027-01-15",
                        status: "active"
                    },
                    {
                        id: 2,
                        licensePlate: "30A-54321",
                        ownerName: "Trần Thị B",
                        ownerID: "001234567891",
                        vehicleType: "SUV",
                        brand: "Honda",
                        model: "CR-V",
                        color: "Trắng",
                        registrationDate: "2021-05-20",
                        expiryDate: "2026-05-20",
                        status: "active"
                    },
                    {
                        id: 3,
                        licensePlate: "31A-98765",
                        ownerName: "Lê Văn C",
                        ownerID: "001234567892",
                        vehicleType: "Hatchback",
                        brand: "Mazda",
                        model: "Mazda3",
                        color: "Đỏ",
                        registrationDate: "2020-11-10",
                        expiryDate: "2025-11-10",
                        status: "active"
                    },
                    {
                        id: 4,
                        licensePlate: "32A-11111",
                        ownerName: "Phạm Văn D",
                        ownerID: "001234567893",
                        vehicleType: "Sedan",
                        brand: "Hyundai",
                        model: "Elantra",
                        color: "Xanh",
                        registrationDate: "2023-03-12",
                        expiryDate: "2028-03-12",
                        status: "active"
                    },
                    {
                        id: 5,
                        licensePlate: "33A-22222",
                        ownerName: "Hoàng Thị E",
                        ownerID: "001234567894",
                        vehicleType: "SUV",
                        brand: "Ford",
                        model: "EcoSport",
                        color: "Bạc",
                        registrationDate: "2022-07-08",
                        expiryDate: "2027-07-08",
                        status: "active"
                    },
                    {
                        id: 6,
                        licensePlate: "34A-33333",
                        ownerName: "Vũ Văn F",
                        ownerID: "001234567895",
                        vehicleType: "Pickup",
                        brand: "Isuzu",
                        model: "D-Max",
                        color: "Xám",
                        registrationDate: "2021-12-25",
                        expiryDate: "2026-12-25",
                        status: "active"
                    },
                    {
                        id: 7,
                        licensePlate: "35A-44444",
                        ownerName: "Đặng Thị G",
                        ownerID: "001234567896",
                        vehicleType: "Hatchback",
                        brand: "Kia",
                        model: "Morning",
                        color: "Vàng",
                        registrationDate: "2023-01-30",
                        expiryDate: "2028-01-30",
                        status: "active"
                    },
                    {
                        id: 8,
                        licensePlate: "36A-55555",
                        ownerName: "Bùi Văn H",
                        ownerID: "001234567897",
                        vehicleType: "Sedan",
                        brand: "Nissan",
                        model: "Sunny",
                        color: "Tím",
                        registrationDate: "2022-09-14",
                        expiryDate: "2027-09-14",
                        status: "expired"
                    },
                    {
                        id: 9,
                        licensePlate: "37A-66666",
                        ownerName: "Lý Thị I",
                        ownerID: "001234567898",
                        vehicleType: "SUV",
                        brand: "Mitsubishi",
                        model: "Outlander",
                        color: "Nâu",
                        registrationDate: "2020-04-18",
                        expiryDate: "2025-04-18",
                        status: "active"
                    },
                    {
                        id: 10,
                        licensePlate: "38A-77777",
                        ownerName: "Cao Văn K",
                        ownerID: "001234567899",
                        vehicleType: "Sedan",
                        brand: "Volkswagen",
                        model: "Jetta",
                        color: "Cam",
                        registrationDate: "2023-06-22",
                        expiryDate: "2028-06-22",
                        status: "suspended"
                    }
                ];
            } catch (error) {
                console.error("Get all vehicles error:", error);
                throw error;
            }
        },
        getVehicleById: async (id) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                // Thử kết nối API trước
                try {
                    const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                        method: "GET",
                        headers: {
                            "x-auth-token": token,
                        },
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (apiError) {
                    console.log("API not available, using mock data");
                }

                // Nếu API không khả dụng, tìm trong dữ liệu mock
                const mockVehicles = await API.vehicle.getAllVehicles();
                const vehicle = mockVehicles.find(v => v.id == id);

                if (!vehicle) {
                    throw new Error("Vehicle not found");
                }

                return vehicle;
            } catch (error) {
                console.error("Get vehicle by ID error:", error);
                throw error;
            }
        },
        getVehicleByLicensePlate: async (licensePlate) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                // Thử kết nối API trước
                try {
                    const response = await fetch(`${API_URL}/api/vehicles/plate/${licensePlate}`, {
                        method: "GET",
                        headers: {
                            "x-auth-token": token,
                        },
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (apiError) {
                    console.log("API not available, using mock data");
                }

                // Nếu API không khả dụng, tìm trong dữ liệu mock
                const mockVehicles = await API.vehicle.getAllVehicles();
                const vehicle = mockVehicles.find(v => v.licensePlate === licensePlate);

                if (!vehicle) {
                    throw new Error("Vehicle not found");
                }

                return vehicle;
            } catch (error) {
                console.error("Get vehicle by license plate error:", error);
                throw error;
            }
        },
        createVehicle: async (vehicleData) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                // Thử kết nối API trước
                try {
                    const response = await fetch(`${API_URL}/api/vehicles`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "x-auth-token": token,
                        },
                        body: JSON.stringify(vehicleData),
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (apiError) {
                    console.log("API not available, using mock response");
                }

                // Mock response cho create vehicle
                return {
                    success: true,
                    message: "Vehicle created successfully (mock)",
                    vehicle: {
                        id: Date.now(), // Mock ID
                        ...vehicleData,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                };
            } catch (error) {
                console.error("Create vehicle error:", error);
                throw error;
            }
        },
        updateVehicle: async (id, vehicleData) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                // Thử kết nối API trước
                try {
                    const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            "x-auth-token": token,
                        },
                        body: JSON.stringify(vehicleData),
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (apiError) {
                    console.log("API not available, using mock response");
                }

                // Mock response cho update vehicle
                return {
                    success: true,
                    message: "Vehicle updated successfully (mock)",
                    vehicle: {
                        id: id,
                        ...vehicleData,
                        updatedAt: new Date().toISOString()
                    }
                };
            } catch (error) {
                console.error("Update vehicle error:", error);
                throw error;
            }
        },
        deleteVehicle: async (id) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                // Thử kết nối API trước
                try {
                    const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                        method: "DELETE",
                        headers: {
                            "x-auth-token": token,
                        },
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (apiError) {
                    console.log("API not available, using mock response");
                }

                // Mock response cho delete vehicle
                return {
                    success: true,
                    message: "Vehicle deleted successfully (mock)"
                };
            } catch (error) {
                console.error("Delete vehicle error:", error);
                throw error;
            }
        }
    },
    training: {
        // ... các API liên quan đến training
    },
    prediction: {
        // ... các API liên quan đến prediction
    },
    // Thêm API cho thống kê mô hình
    stats: {
        getModelPerformanceStats: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/model-performance`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get model stats error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        },
        getTrainingTrends: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/training-trends`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get training trends error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        },
        getPredictionUsage: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/prediction-usage`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get prediction usage error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        }
    }
};

// For use in browser
window.API = API;

