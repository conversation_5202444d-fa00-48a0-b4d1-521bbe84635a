// API endpoints
const API_URL = "http://localhost:3000";

// API object
const API = {
    auth: {
        login: async (username, password) => {
            try {
                const response = await fetch(`${API_URL}/api/auth/login`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ username, password }),
                });
                return await response.json();
            } catch (error) {
                console.error("Login error:", error);
                return { success: false, message: "Lỗi kết nối đến máy chủ" };
            }
        },
        verify: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/auth/verify`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Verify error:", error);
                return { success: false };
            }
        },
        getProfile: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/auth/me`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get profile error:", error);
                return { success: false };
            }
        }
    },
    vehicle: {
        getAllVehicles: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Get all vehicles error:", error);
                throw error;
            }
        },
        getVehicleById: async (id) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Get vehicle by ID error:", error);
                throw error;
            }
        },
        getVehicleByLicensePlate: async (licensePlate) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/plate/${licensePlate}`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Get vehicle by license plate error:", error);
                throw error;
            }
        },
        createVehicle: async (vehicleData) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "x-auth-token": token,
                    },
                    body: JSON.stringify(vehicleData),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Create vehicle error:", error);
                throw error;
            }
        },
        updateVehicle: async (id, vehicleData) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json",
                        "x-auth-token": token,
                    },
                    body: JSON.stringify(vehicleData),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Update vehicle error:", error);
                throw error;
            }
        },
        deleteVehicle: async (id) => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/vehicles/${id}`, {
                    method: "DELETE",
                    headers: {
                        "x-auth-token": token,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error("Delete vehicle error:", error);
                throw error;
            }
        }
    },
    training: {
        // ... các API liên quan đến training
    },
    prediction: {
        // ... các API liên quan đến prediction
    },
    // Thêm API cho thống kê mô hình
    stats: {
        getModelPerformanceStats: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/model-performance`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get model stats error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        },
        getTrainingTrends: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/training-trends`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get training trends error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        },
        getPredictionUsage: async () => {
            try {
                const token = localStorage.getItem("token");
                if (!token) return { success: false };

                const response = await fetch(`${API_URL}/api/stats/prediction-usage`, {
                    method: "GET",
                    headers: {
                        "x-auth-token": token,
                    },
                });
                return await response.json();
            } catch (error) {
                console.error("Get prediction usage error:", error);
                return { success: false, error: "Lỗi kết nối đến máy chủ" };
            }
        }
    }
};

// For use in browser
window.API = API;

