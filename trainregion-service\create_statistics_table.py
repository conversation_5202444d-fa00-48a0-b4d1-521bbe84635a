#!/usr/bin/env python3
"""
Migration script to create training_statistics table
"""

import os
import sys
import mysql.connector
from datetime import datetime

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'trainregion-db'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'user': os.getenv('DB_USER', 'trainregion_user'),
    'password': os.getenv('DB_PASSWORD', '123456'),
    'database': os.getenv('DB_NAME', 'trainregion_db')
}

def create_training_statistics_table():
    """Create training_statistics table"""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS training_statistics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        
        -- Training Job Info
        original_job_id INT NOT NULL,
        job_name VARCHAR(255) NOT NULL,
        
        -- Dataset Info
        dataset_id INT,
        dataset_name VARCHAR(255),
        dataset_size INT DEFAULT 0,
        
        -- Training Configuration
        model_type VARCHAR(50) NOT NULL,
        epochs INT NOT NULL,
        batch_size INT NOT NULL,
        learning_rate FLOAT NOT NULL,
        image_size INT DEFAULT 640,
        
        -- Training Results
        training_time FLOAT DEFAULT 0.0,
        final_epoch INT DEFAULT 0,
        final_map50 FLOAT DEFAULT 0.0,
        final_map95 FLOAT DEFAULT 0.0,
        best_map50 FLOAT DEFAULT 0.0,
        best_map95 FLOAT DEFAULT 0.0,
        
        -- Status & Outcome
        status ENUM('running', 'completed', 'failed', 'stopped') DEFAULT 'running',
        outcome ENUM('saved', 'discarded', 'failed'),
        
        -- Model Info (if saved)
        saved_model_id INT,
        saved_model_name VARCHAR(255),
        
        -- Timestamps
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        decided_at TIMESTAMP NULL,
        
        -- Additional Info
        notes TEXT,
        error_message TEXT,
        
        -- Metadata
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- Indexes
        INDEX idx_original_job_id (original_job_id),
        INDEX idx_dataset_id (dataset_id),
        INDEX idx_model_type (model_type),
        INDEX idx_status (status),
        INDEX idx_outcome (outcome),
        INDEX idx_started_at (started_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
    
    try:
        # Connect to database
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("Creating training_statistics table...")
        cursor.execute(create_table_sql)
        
        # Check if table was created
        cursor.execute("SHOW TABLES LIKE 'training_statistics'")
        result = cursor.fetchone()
        
        if result:
            print("✅ training_statistics table created successfully!")
            
            # Show table structure
            cursor.execute("DESCRIBE training_statistics")
            columns = cursor.fetchall()
            
            print("\n📋 Table structure:")
            for column in columns:
                print(f"  {column[0]} - {column[1]} - {column[2]} - {column[3]}")
                
        else:
            print("❌ Failed to create training_statistics table")
            
        connection.commit()
        
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("Database connection closed.")
    
    return True

def migrate_existing_jobs():
    """Migrate existing training jobs to statistics table"""
    
    migrate_sql = """
    INSERT INTO training_statistics (
        original_job_id, job_name, dataset_id, model_type, epochs, batch_size, 
        learning_rate, image_size, training_time, final_map50, final_map95,
        best_map50, best_map95, status, outcome, started_at, completed_at
    )
    SELECT 
        tj.id as original_job_id,
        tj.name as job_name,
        tj.dataset_id,
        tj.model_type,
        tj.epochs,
        tj.batch_size,
        tj.learning_rate,
        tj.image_size,
        tj.training_time,
        tj.best_map50 as final_map50,
        tj.best_map95 as final_map95,
        tj.best_map50,
        tj.best_map95,
        tj.status,
        CASE 
            WHEN tj.model_saved = 1 THEN 'saved'
            WHEN tj.model_saved = 0 THEN 'discarded'
            ELSE NULL
        END as outcome,
        tj.created_at as started_at,
        tj.updated_at as completed_at
    FROM train_jobs tj
    WHERE tj.id NOT IN (
        SELECT original_job_id FROM training_statistics
    );
    """
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("Migrating existing training jobs...")
        cursor.execute(migrate_sql)
        migrated_count = cursor.rowcount
        
        connection.commit()
        print(f"✅ Migrated {migrated_count} existing training jobs to statistics table")
        
    except mysql.connector.Error as e:
        print(f"❌ Migration error: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
    
    return True

if __name__ == "__main__":
    print("🚀 Creating training_statistics table...")
    
    if create_training_statistics_table():
        print("\n🔄 Migrating existing data...")
        migrate_existing_jobs()
        print("\n✅ Migration completed successfully!")
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)
