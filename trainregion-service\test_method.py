#!/usr/bin/env python3

import sys
sys.path.append('/app')

from controllers.training_controller import TrainingController

tc = TrainingController()

print("Methods in TrainingController:")
for method in dir(tc):
    if not method.startswith('_'):
        print(f"  {method}")

print(f"\nHas save_model_manual: {hasattr(tc, 'save_model_manual')}")
print(f"Has save_model_from_job: {hasattr(tc, 'save_model_from_job')}")

# Try to read the file directly
print("\nReading training_controller.py directly:")
with open('/app/controllers/training_controller.py', 'r') as f:
    content = f.read()
    if 'save_model_manual' in content:
        print("✅ save_model_manual found in file")
    else:
        print("❌ save_model_manual NOT found in file")
        
    if 'save_model_from_job' in content:
        print("✅ save_model_from_job found in file")
    else:
        print("❌ save_model_from_job NOT found in file")
