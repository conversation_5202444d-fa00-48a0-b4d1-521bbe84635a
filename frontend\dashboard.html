<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard - <PERSON><PERSON> thống quản lý biển số xe</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
      .sidebar {
        min-height: 100vh;
        background-color: #343a40;
      }
      .sidebar .nav-link {
        color: #fff;
        padding: 15px 20px;
      }
      .sidebar .nav-link:hover {
        background-color: #495057;
        color: #fff;
      }
      .sidebar .nav-link.active {
        background-color: #007bff;
        color: #fff;
      }
      .main-content {
        padding: 20px;
      }
      .vehicle-card {
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-2 d-md-block sidebar">
          <div class="position-sticky pt-3">
            <h5 class="text-white text-center mb-4">Hệ thống quản lý</h5>
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link active" href="dashboard.html">
                  <i class="fas fa-tachometer-alt me-2"></i>Tổng quan
                </a>
              </li>
              <li class="nav-item" id="add-vehicle-nav">
                <a class="nav-link" href="add-vehicle.html">
                  <i class="fas fa-plus me-2"></i>Thêm xe mới
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="ai-training.html">
                  <i class="fas fa-brain me-2"></i>Huấn luyện AI
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="ai-predict.html">
                  <i class="fas fa-search me-2"></i>Dự đoán AI
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="model-stats.html">
                  <i class="fas fa-chart-bar me-2"></i>Thống kê mô hình
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" onclick="logout()">
                  <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                </a>
              </li>
            </ul>
            <div class="mt-4 px-3">
              <div class="text-white-50 small">
                <div id="user-info-sidebar"></div>
              </div>
            </div>
          </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-10 ms-sm-auto main-content">
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Danh sách đăng ký xe</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <button type="button" class="btn btn-success" id="add-vehicle-btn">
                <i class="fas fa-plus me-1"></i>Thêm xe mới
              </button>
            </div>
          </div>

          <form class="mb-4" id="search-form">
            <div class="row align-items-end">
              <div class="col-md-8">
                <div class="mb-3">
                  <label for="search-term" class="form-label">Tìm kiếm theo biển số xe</label>
                  <input
                    type="text"
                    class="form-control"
                    id="search-term"
                    placeholder="Nhập biển số xe"
                  />
                </div>
              </div>
              <div class="col-md-4 d-flex gap-2">
                <button type="submit" class="btn btn-primary" id="search-btn">
                  <i class="fas fa-search me-1"></i>Tìm kiếm
                </button>
                <button
                  type="button"
                  class="btn btn-secondary"
                  id="reset-search-btn"
                >
                  <i class="fas fa-undo me-1"></i>Đặt lại
                </button>
              </div>
            </div>
          </form>

          <div class="row" id="vehicles-list">
            <!-- Vehicle cards will be added here -->
          </div>
        </main>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script>
      // Check if user is logged in
      const userStr = localStorage.getItem("user");
      const token = localStorage.getItem("token");

      let user = null;
      try {
        user = userStr ? JSON.parse(userStr) : null;
      } catch (e) {
        console.error("Error parsing user data:", e);
        user = null;
      }

      if (!user || !token) {
        window.location.href = "login.html";
      } else {

      // Update UI based on user role
      document.getElementById("user-info-sidebar").innerHTML = `
        <strong>${user.fullName}</strong><br>
        <small>${user.role === "admin" ? "Quản trị viên" : "Cảnh sát"}</small>
      `;

      // Only show Add Vehicle button for admin
      if (user.role !== "admin") {
        document.getElementById("add-vehicle-btn").style.display = "none";
        document.getElementById("add-vehicle-nav").style.display = "none";
      }

      // Logout functionality
      function logout() {
        localStorage.removeItem("user");
        localStorage.removeItem("token");
        window.location.href = "login.html";
      }

      // Render vehicles
      async function renderVehicles() {
        try {
          const vehicles = await API.vehicle.getAllVehicles();

          // Lưu danh sách xe vào localStorage để sử dụng trong add-vehicle.html
          localStorage.setItem("allVehicles", JSON.stringify(vehicles));

          const container = document.getElementById("vehicles-list");
          container.innerHTML = "";

          // Render từng xe
          vehicles.forEach((vehicle, index) => {
            const card = document.createElement("div");
            card.className = "col-md-6 col-lg-4 mb-4";

            let badgeClass = "bg-secondary";
            let statusText = "Không xác định";
            switch (vehicle.status) {
              case "active":
                badgeClass = "bg-success";
                statusText = "Hoạt động";
                break;
              case "expired":
                badgeClass = "bg-danger";
                statusText = "Hết hạn";
                break;
              case "suspended":
                badgeClass = "bg-warning";
                statusText = "Tạm dừng";
                break;
            }

            // Tính toán index của vehicle kế tiếp
            const nextIndex = (index + 1) % vehicles.length;
            const nextVehicleId = vehicles[nextIndex].id;

            // Tạo các nút hành động (chỉ cho admin)
            const adminActions =
              user.role === "admin"
                ? `<div class="d-flex gap-2">
                            <button class="btn btn-primary edit-btn" data-id="${vehicle.id}" data-next-id="${nextVehicleId}">Sửa</button>
                            <button class="btn btn-danger delete-btn" data-id="${vehicle.id}">Xóa</button>
                          </div>`
                : "";

            // Tạo HTML cho card xe
            card.innerHTML = `
                        <div class="card vehicle-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    ${vehicle.licensePlate}
                                    <span class="badge ${badgeClass}">${statusText}</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">${vehicle.brand} ${
              vehicle.model
            }</h5>
                                <p class="card-text">
                                    <strong>Chủ sở hữu:</strong> ${
                                      vehicle.ownerName
                                    }<br>
                                    <strong>CMND/CCCD:</strong> ${
                                      vehicle.ownerID
                                    }<br>
                                    <strong>Loại xe:</strong> ${
                                      vehicle.vehicleType
                                    }<br>
                                    <strong>Màu sắc:</strong> ${
                                      vehicle.color
                                    }<br>
                                    <strong>Ngày đăng ký:</strong> ${new Date(
                                      vehicle.registrationDate
                                    ).toLocaleDateString("vi-VN")}<br>
                                    <strong>Ngày hết hạn:</strong> ${new Date(
                                      vehicle.expiryDate
                                    ).toLocaleDateString("vi-VN")}
                                </p>
                                ${adminActions}
                            </div>
                        </div>
                    `;

            container.appendChild(card);
          });

          // Thêm event listeners cho các nút (nếu là admin)
          if (user.role === "admin") {
            document.querySelectorAll(".edit-btn").forEach((btn) => {
              btn.addEventListener("click", function () {
                const currentId = this.getAttribute("data-id");
                const nextId = this.getAttribute("data-next-id");

                // Chuyển hướng đến trang sửa với ID của vehicle kế tiếp
                window.location.href = `add-vehicle.html?id=${currentId}&nextId=${nextId}`;
              });
            });

            document.querySelectorAll(".delete-btn").forEach((btn) => {
              btn.addEventListener("click", async function () {
                const id = this.getAttribute("data-id");
                if (confirm("Bạn có chắc chắn muốn xóa xe này không?")) {
                  try {
                    await API.vehicle.deleteVehicle(id);
                    this.closest(".col-md-6").remove();
                  } catch (error) {
                    alert("Lỗi khi xóa xe: " + error.message);
                  }
                }
              });
            });
          }
        } catch (error) {
          console.error("Error rendering vehicles:", error);
          alert("Lỗi khi tải danh sách xe: " + error.message);
        }
      }

      // Initial render
      renderVehicles();

      // Search functionality
      document
        .getElementById("search-form")
        .addEventListener("submit", async function (e) {
          e.preventDefault();
          const term = document.getElementById("search-term").value.trim();

          if (term) {
            try {
              const vehicle = await API.vehicle.getVehicleByLicensePlate(term);
              const container = document.getElementById("vehicles-list");
              container.innerHTML = "";

              // Render single vehicle
              const card = document.createElement("div");
              card.className = "col-md-6 mb-4";

              // Determine status badge
              let badgeClass = "bg-success";
              let statusText = "Hoạt động";

              switch (vehicle.status) {
                case "active":
                  badgeClass = "bg-success";
                  statusText = "Hoạt động";
                  break;
                case "expired":
                  badgeClass = "bg-danger";
                  statusText = "Hết hạn";
                  break;
                case "suspended":
                  badgeClass = "bg-warning";
                  statusText = "Tạm dừng";
                  break;
              }

              const adminActions =
                user.role === "admin"
                  ? `<div class="d-flex gap-2">
                              <button class="btn btn-primary edit-btn" data-id="${vehicle.id}">Sửa</button>
                              <button class="btn btn-danger delete-btn" data-id="${vehicle.id}">Xóa</button>
                            </div>`
                  : "";

              card.innerHTML = `
                          <div class="card vehicle-card">
                              <div class="card-header d-flex justify-content-between align-items-center">
                                  <h5 class="mb-0">
                                      ${vehicle.licensePlate}
                                      <span class="badge ${badgeClass}">${statusText}</span>
                                  </h5>
                              </div>
                              <div class="card-body">
                                  <h5 class="card-title">${vehicle.brand} ${
                vehicle.model
              }</h5>
                                  <p class="card-text">
                                      <strong>Chủ sở hữu:</strong> ${
                                        vehicle.ownerName
                                      }<br>
                                      <strong>CMND/CCCD:</strong> ${
                                        vehicle.ownerID
                                      }<br>
                                      <strong>Loại xe:</strong> ${
                                        vehicle.vehicleType
                                      }<br>
                                      <strong>Màu sắc:</strong> ${
                                        vehicle.color
                                      }<br>
                                      <strong>Ngày đăng ký:</strong> ${new Date(
                                        vehicle.registrationDate
                                      ).toLocaleDateString("vi-VN")}<br>
                                      <strong>Ngày hết hạn:</strong> ${new Date(
                                        vehicle.expiryDate
                                      ).toLocaleDateString("vi-VN")}
                                  </p>
                                  ${adminActions}
                              </div>
                          </div>
                      `;

              container.appendChild(card);

              // Add event listeners to buttons
              if (user.role === "admin") {
                document.querySelectorAll(".edit-btn").forEach((btn) => {
                  btn.addEventListener("click", function () {
                    const id = this.getAttribute("data-id");
                    window.location.href = `add-vehicle.html?id=${id}`;
                  });
                });

                document.querySelectorAll(".delete-btn").forEach((btn) => {
                  btn.addEventListener("click", async function () {
                    const id = this.getAttribute("data-id");
                    if (confirm("Bạn có chắc chắn muốn xóa xe này không?")) {
                      try {
                        await API.vehicle.deleteVehicle(id);
                        this.closest(".col-md-6").remove();
                      } catch (error) {
                        alert("Lỗi khi xóa xe: " + error.message);
                      }
                    }
                  });
                });
              }
            } catch (error) {
              alert("Không tìm thấy xe với biển số này: " + error.message);
            }
          } else {
            renderVehicles();
          }
        });

      // Reset search
      document
        .getElementById("reset-search-btn")
        .addEventListener("click", function () {
          document.getElementById("search-term").value = "";
          renderVehicles();
        });

      // Add vehicle button
      document
        .getElementById("add-vehicle-btn")
        .addEventListener("click", function () {
          window.location.href = "add-vehicle.html";
        });
      } // End of else block
    </script>
  </body>
</html>

