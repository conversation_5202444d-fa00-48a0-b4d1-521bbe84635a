# KẾ HOẠCH CHUYỂN ĐỔI TRAINREGION-SERVICE THÀNH MICROSERVICE MVC

## 1. TỔNG QUAN DỰ ÁN

### 1.1 Mục tiêu
- Chuyển đổi trainregion-service từ script đơn lẻ thành microservice hoàn chỉnh theo mô hình MVC
- Tích hợp với hệ thống hethongbienso hiện tại (sử dụng Docker Compose)
- Sử dụng dataset từ Label Studio đã có sẵn
- Workflow approval trước khi lưu model vào database
- Tích hợp với frontend hiện tại

### 1.2 Yêu cầu chức năng mới
- Upload ảnh từ thiết bị để tạo training dataset
- C<PERSON>u hình thông số training (epochs, batch_size, model_type, etc.)
- Theo dõi quá trình training
- Xem kết quả training và quyết định approve/reject
- API predict với model đã được approve
- Quản lý models đã train

## 2. PHÂN TÍCH CẤU TRÚC HIỆN TẠI

### 2.1 Trainregion-service hiện tại
```
trainregion-service/
├── main.py (training script cơ bản)
├── predict.py (prediction script)
├── mydata.yaml (YOLO config)
├── requirements.txt
├── dataset/ (dataset cũ)
├── dataset-labelstudio/ (dataset từ Label Studio - SỬ DỤNG)
│   ├── images/ (49 ảnh đã gán nhãn)
│   ├── labels/ (49 file .txt YOLO format)
│   ├── classes.txt
│   └── notes.json
├── runs/ (kết quả training)
├── image-test/ (ảnh test)
├── yolov8n.pt, yolov8s.pt, yolov8x.pt (pretrained models)
└── venv/ (virtual environment)
```

### 2.2 Database hiện tại (trainregion-init.sql)
```sql
- models (id, name, type, version, epochs, batch_size, dataset_size, training_time, map50, map95, model_path, created_at, updated_at)
- train_jobs (id, model_id, status, started_at, completed_at, error_message, created_at, updated_at)
- predict_logs (id, model_id, image_path, result_path, num_detections, confidence_scores, bounding_boxes, processing_time, created_at)
```

## 3. KIẾN TRÚC MỚI ĐỀ XUẤT (SỬ DỤNG CẤU TRÚC HIỆN TẠI)

### 3.1 Cấu trúc thư mục mới (Tối ưu từ hiện tại)
```
trainregion-service/
├── app.py (Flask entry point - MỚI)
├── models/ (Database models - ĐÃ CÓ, CẬP NHẬT)
│   ├── __init__.py
│   ├── model.py (cập nhật thêm approval fields)
│   ├── training_job.py (cập nhật)
│   └── predict_log.py (đã có)
├── controllers/ (Business logic - ĐÃ CÓ, CẬP NHẬT)
│   ├── __init__.py
│   ├── model_controller.py (đã có, cập nhật)
│   ├── training_controller.py (MỚI)
│   └── predict_controller.py (đã có)
├── routes/ (API endpoints - ĐÃ CÓ, CẬP NHẬT)
│   ├── __init__.py
│   ├── model_routes.py (đã có)
│   ├── training_routes.py (MỚI)
│   └── predict_routes.py (đã có)
├── utils/ (Helper functions - MỚI)
│   ├── __init__.py
│   ├── dataset_utils.py (xử lý dataset-labelstudio)
│   └── training_utils.py (logic training)
├── config/ (Configuration - ĐÃ CÓ)
│   └── config.py (đã có)
├── dataset-labelstudio/ (Dataset từ Label Studio - ĐÃ CÓ)
│   ├── images/ (49 ảnh)
│   ├── labels/ (49 labels)
│   └── classes.txt
├── runs/ (Training results - ĐÃ CÓ)
├── image-test/ (Test images - ĐÃ CÓ)
├── main.py (training script - CHUYỂN THÀNH UTILS)
├── predict.py (prediction script - CHUYỂN THÀNH CONTROLLER)
├── mydata.yaml (YOLO config - GIỮ NGUYÊN)
├── requirements.txt (đã có)
├── Dockerfile (MỚI)
├── yolov8*.pt (pretrained models - ĐÃ CÓ)
└── venv/ (development only)
```

### 3.2 Tác dụng từng thành phần

#### 3.2.1 Core Files
- **app.py**: Flask application entry point, đăng ký routes, CORS, database connection
- **Dockerfile**: Container configuration cho deployment
- **requirements.txt**: Python dependencies (đã có)

#### 3.2.2 Models (Database ORM)
- **model.py**: Model entity với thêm fields approval (is_approved, approval_notes)
- **training_job.py**: Training job entity với progress tracking
- **predict_log.py**: Prediction history (đã có)

#### 3.2.3 Controllers (Business Logic)
- **model_controller.py**: CRUD operations cho models, approval workflow
- **training_controller.py**: Start/stop training, progress monitoring
- **predict_controller.py**: Prediction với model selection

#### 3.2.4 Routes (API Endpoints)
- **model_routes.py**: /api/models/* endpoints
- **training_routes.py**: /api/training/* endpoints
- **predict_routes.py**: /api/predict/* endpoints

#### 3.2.5 Utils (Helper Functions)
- **dataset_utils.py**: Xử lý dataset-labelstudio, tạo train/val split
- **training_utils.py**: Core training logic từ main.py

#### 3.2.6 Data Directories
- **dataset-labelstudio/**: Source dataset từ Label Studio (49 ảnh + labels)
- **runs/**: Training results và model outputs
- **image-test/**: Test images cho prediction

## 4. DATABASE SCHEMA CẬP NHẬT (SỬ DỤNG HIỆN TẠI)

### 4.1 Thêm bảng datasets (quản lý dataset upload)
```sql
-- Thêm vào trainregion-init.sql
CREATE TABLE IF NOT EXISTS datasets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    upload_path VARCHAR(500) NOT NULL,
    image_count INT DEFAULT 0,
    file_size BIGINT DEFAULT 0,
    upload_type ENUM('zip', 'images') DEFAULT 'zip',
    has_labels BOOLEAN DEFAULT FALSE,
    status ENUM('uploaded', 'processing', 'ready', 'error') DEFAULT 'uploaded',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4.2 Cập nhật bảng models (thêm approval workflow)
```sql
-- Thêm vào trainregion-init.sql
ALTER TABLE models ADD COLUMN is_approved BOOLEAN DEFAULT FALSE;
ALTER TABLE models ADD COLUMN approval_notes TEXT;
ALTER TABLE models ADD COLUMN dataset_id INT;
ALTER TABLE models ADD COLUMN train_val_split FLOAT DEFAULT 0.8;
ALTER TABLE models ADD FOREIGN KEY (dataset_id) REFERENCES datasets(id);
```

### 4.3 Cập nhật bảng train_jobs (thêm thông tin training)
```sql
-- Cập nhật bảng train_jobs hiện tại
ALTER TABLE train_jobs ADD COLUMN name VARCHAR(255);
ALTER TABLE train_jobs ADD COLUMN dataset_id INT;
ALTER TABLE train_jobs ADD COLUMN model_type ENUM('yolov8n', 'yolov8s', 'yolov8m', 'yolov8l', 'yolov8x') DEFAULT 'yolov8n';
ALTER TABLE train_jobs ADD COLUMN epochs INT DEFAULT 100;
ALTER TABLE train_jobs ADD COLUMN batch_size INT DEFAULT 16;
ALTER TABLE train_jobs ADD COLUMN learning_rate FLOAT DEFAULT 0.01;
ALTER TABLE train_jobs ADD COLUMN image_size INT DEFAULT 640;
ALTER TABLE train_jobs ADD COLUMN progress FLOAT DEFAULT 0.0;
ALTER TABLE train_jobs ADD COLUMN current_epoch INT DEFAULT 0;
ALTER TABLE train_jobs ADD COLUMN best_map50 FLOAT DEFAULT 0.0;
ALTER TABLE train_jobs ADD COLUMN best_map95 FLOAT DEFAULT 0.0;
ALTER TABLE train_jobs ADD COLUMN training_time FLOAT DEFAULT 0.0;
ALTER TABLE train_jobs ADD COLUMN result_path VARCHAR(500);
ALTER TABLE train_jobs ADD FOREIGN KEY (dataset_id) REFERENCES datasets(id);
```

## 5. API ENDPOINTS (SỬ DỤNG VÀ CẬP NHẬT HIỆN TẠI)

### 5.1 Dataset Management (MỚI - upload từ thiết bị)
- `POST /api/dataset/upload-zip` - Upload dataset ZIP file (có cả images và labels)
- `POST /api/dataset/upload-images` - Upload nhiều ảnh trực tiếp (chỉ có images)
- `GET /api/dataset/` - List all uploaded datasets
- `GET /api/dataset/{id}` - Get dataset details
- `DELETE /api/dataset/{id}` - Delete dataset
- `POST /api/dataset/{id}/validate` - Validate dataset structure

### 5.2 Training Management (MỚI)
- `POST /api/training/start` - Start training job với config
- `GET /api/training/jobs` - List training jobs
- `GET /api/training/jobs/{id}` - Get training job details
- `GET /api/training/jobs/{id}/progress` - Get training progress
- `POST /api/training/jobs/{id}/stop` - Stop training job
- `DELETE /api/training/jobs/{id}` - Delete training job

### 5.3 Model Management (CẬP NHẬT HIỆN TẠI)
- `GET /api/model/` - List models (ĐÃ CÓ)
- `GET /api/model/{id}` - Get model details (ĐÃ CÓ)
- `POST /api/model/{id}/approve` - Approve model for use (MỚI)
- `POST /api/model/{id}/reject` - Reject model (MỚI)
- `DELETE /api/model/{id}` - Delete model (ĐÃ CÓ)
- `GET /api/model/stats` - Get model statistics (ĐÃ CÓ)

### 5.4 Prediction (ĐÃ CÓ)
- `POST /api/predict` - Predict with selected model (ĐÃ CÓ)
- `GET /api/predict/history` - Get prediction history (ĐÃ CÓ)

## 6. QUY TRÌNH TRAINING MỚI (SỬ DỤNG DATASET-LABELSTUDIO)

### 6.1 Workflow mới (2 cách upload)

#### Cách 1: Upload ZIP file (có labels)
1. **Upload ZIP**: User upload file ZIP chứa images/, labels/, classes.txt
2. **Validate Structure**: Kiểm tra cấu trúc hoàn chỉnh
3. **Ready for Training**: Dataset sẵn sàng training ngay

#### Cách 2: Upload Images (chỉ có ảnh)
1. **Upload Images**: User chọn nhiều ảnh từ thiết bị
2. **Auto-generate Labels**: Hệ thống tạo labels rỗng hoặc sử dụng pre-trained model để tạo labels tự động
3. **Manual Labeling**: (Tùy chọn) User có thể label thủ công sau
4. **Ready for Training**: Dataset sẵn sàng training

#### Workflow chung
5. **Configure Training**: Chọn dataset và thông số training
6. **Start Training**: Bắt đầu training với YOLO
7. **Monitor Progress**: Theo dõi progress qua API polling
8. **Review Results**: Xem metrics (mAP50, mAP95, loss curves)
9. **Approve/Reject**: Admin quyết định approve model
10. **Use Model**: Model approved có thể dùng cho prediction

### 6.2 Training Process Details (Cập nhật từ main.py)
```python
def start_training_job(job_config):
    # 1. Prepare dataset từ uploaded dataset
    dataset_id = job_config['dataset_id']
    dataset_path = get_dataset_path(dataset_id)

    # 2. Tạo train/val split từ dataset
    train_images, val_images = create_train_val_split_from_dataset(
        dataset_path,
        split_ratio=job_config['train_val_split']
    )

    # 3. Tạo data.yaml cho job này
    create_job_yaml(dataset_path, job_config['job_id'])

    # 4. Initialize YOLO model (từ main.py)
    model = YOLO(f"{job_config['model_type']}.pt")

    # 5. Start training (từ main.py)
    results = model.train(
        data=f"runs/train/job_{job_config['job_id']}/data.yaml",
        epochs=job_config['epochs'],
        batch=job_config['batch_size'],
        lr0=job_config.get('learning_rate', 0.01),
        imgsz=job_config.get('image_size', 640),
        project=f"runs/train",
        name=f"job_{job_config['job_id']}"
    )

    # 6. Save results to database
    save_training_results(job_config['job_id'], results)

    # 7. Update job status
    update_job_status(job_config['job_id'], "completed")
```

### 6.3 Dataset Processing (2 cách)

#### A. Xử lý ZIP file
```python
def process_uploaded_zip(dataset_zip_path, dataset_id):
    """
    Xử lý dataset ZIP đã upload (có cả images và labels)
    """
    import zipfile
    import os

    # Extract ZIP file
    extract_path = f"uploads/dataset_{dataset_id}"
    with zipfile.ZipFile(dataset_zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

    # Validate structure
    required_folders = ['images', 'labels']
    required_files = ['classes.txt']

    for folder in required_folders:
        if not os.path.exists(os.path.join(extract_path, folder)):
            raise ValueError(f"Missing required folder: {folder}")

    for file in required_files:
        if not os.path.exists(os.path.join(extract_path, file)):
            raise ValueError(f"Missing required file: {file}")

    # Count images
    images_path = os.path.join(extract_path, 'images')
    image_count = len([f for f in os.listdir(images_path)
                      if f.lower().endswith(('.jpg', '.jpeg', '.png'))])

    return {
        'path': extract_path,
        'image_count': image_count,
        'upload_type': 'zip',
        'has_labels': True,
        'status': 'ready'
    }

#### B. Xử lý Images upload
```python
def process_uploaded_images(image_files, dataset_id, dataset_name):
    """
    Xử lý nhiều ảnh được upload (chỉ có images)
    """
    import os

    # Tạo thư mục dataset
    dataset_path = f"uploads/dataset_{dataset_id}"
    images_path = os.path.join(dataset_path, 'images')
    labels_path = os.path.join(dataset_path, 'labels')

    os.makedirs(images_path, exist_ok=True)
    os.makedirs(labels_path, exist_ok=True)

    # Lưu images
    image_count = 0
    for image_file in image_files:
        if image_file.filename.lower().endswith(('.jpg', '.jpeg', '.png')):
            image_file.save(os.path.join(images_path, image_file.filename))

            # Tạo label file rỗng tương ứng
            label_filename = os.path.splitext(image_file.filename)[0] + '.txt'
            with open(os.path.join(labels_path, label_filename), 'w') as f:
                f.write('')  # Label rỗng, user có thể label sau

            image_count += 1

    # Tạo classes.txt mặc định
    with open(os.path.join(dataset_path, 'classes.txt'), 'w') as f:
        f.write('license_plate\n')

    return {
        'path': dataset_path,
        'image_count': image_count,
        'upload_type': 'images',
        'has_labels': False,  # Labels rỗng, cần label thủ công
        'status': 'ready'
    }

def create_job_yaml(dataset_path, job_id):
    """
    Tạo data.yaml cho training job từ dataset đã upload
    """
    yaml_content = f"""
path: {dataset_path}
train: images
val: images  # Sẽ được split tự động bởi YOLO

names:
  0: license_plate
"""

    os.makedirs(f"runs/train/job_{job_id}", exist_ok=True)
    with open(f"runs/train/job_{job_id}/data.yaml", 'w') as f:
        f.write(yaml_content)
```

## 7. FRONTEND INTEGRATION (SỬ DỤNG HIỆN TẠI)

### 7.1 Cập nhật giao diện AI Predict hiện tại
- **Dataset Upload Options**:
  - Tab 1: Upload ZIP file (có cả images và labels)
  - Tab 2: Upload nhiều ảnh (chỉ có images)
- **Dataset Management**: Hiển thị danh sách datasets với thông tin upload_type và has_labels
- **Training Config**: Form cấu hình (dataset selection, model_type, epochs, batch_size, train/val split)
- **Training Monitor**: Dashboard theo dõi progress training
- **Model Approval**: Interface approve/reject model sau training
- **Model Selection**: Dropdown chọn model đã approved cho prediction

### 7.2 Không cần WebSocket
- Sử dụng API polling để update progress
- Đơn giản hơn và phù hợp với cấu trúc hiện tại

## 8. KẾ HOẠCH THỰC HIỆN (THỰC TẾ)

### Phase 1: Cập nhật Database & Core Structure (3-5 ngày)
- [ ] Cập nhật trainregion-init.sql với các fields mới
- [ ] Tạo utils/dataset_utils.py và utils/training_utils.py
- [ ] Cập nhật models/model.py và models/training_job.py
- [ ] Tạo controllers/training_controller.py
- [ ] Tạo routes/training_routes.py

### Phase 2: Training Logic (3-5 ngày)
- [ ] Implement image selection từ dataset-labelstudio
- [ ] Implement train/val split logic
- [ ] Chuyển logic từ main.py thành training_utils.py
- [ ] Implement training job management
- [ ] Test training workflow

### Phase 3: Model Approval & Integration (2-3 ngày)
- [ ] Implement model approval workflow
- [ ] Cập nhật prediction để chỉ dùng approved models
- [ ] Tích hợp với frontend hiện tại
- [ ] Test end-to-end workflow

### Phase 4: Docker & Deployment (1-2 ngày)
- [ ] Tạo Dockerfile cho trainregion-service
- [ ] Cập nhật docker-compose.yml
- [ ] Test deployment
- [ ] Documentation

## 9. DOCKER INTEGRATION

### 9.1 Dockerfile cho trainregion-service
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 3003

# Run application
CMD ["python", "app.py"]
```

### 9.2 Docker-compose.yml đã có sẵn
```yaml
# Đã có sẵn trong docker-compose.yml hiện tại
trainregion-service:
  build: ./trainregion-service
  container_name: trainregion-service
  restart: always
  ports:
    - "3003:3003"
  environment:
    - DB_HOST=trainregion-db
    - DB_USER=trainregion_user
    - DB_PASS=123456
    - DB_NAME=trainregion_db
    - DB_PORT=3306
  depends_on:
    - trainregion-db
  networks:
    - app-network  # SỬ DỤNG app-network như các service khác
```

### 9.3 Cần thêm volumes cho uploads
```yaml
# Cập nhật trainregion-service trong docker-compose.yml
volumes:
  - ./trainregion-service/runs:/app/runs
  - ./trainregion-service/uploads:/app/uploads  # Thêm để lưu uploaded datasets
```

## 10. MIGRATION STRATEGY (THỰC TẾ)

### 10.1 Backward Compatibility
- Giữ nguyên tất cả API endpoints hiện tại
- Thêm các endpoints mới cho training
- Không thay đổi database schema hiện tại, chỉ thêm fields

### 10.2 Sử dụng tài nguyên hiện có
- Pretrained models: yolov8n.pt, yolov8s.pt, yolov8x.pt
- Database: trainregion_db đã có cấu trúc cơ bản
- Frontend: AI Predict interface đã tích hợp
- Docker: trainregion-service đã được cấu hình trong docker-compose.yml
- Network: app-network đã được thiết lập cho tất cả services

## 11. CHI TIẾT IMPLEMENTATION

### 11.1 Dataset Upload Process (2 options)

#### Option 1: Upload ZIP file
```
1. User vào trang Training
2. Chọn tab "Upload ZIP Dataset"
3. Chọn file ZIP từ thiết bị (chứa images/, labels/, classes.txt)
4. Upload và validate dataset structure
5. Hiển thị dataset info (tên, số ảnh, has_labels: true)
6. Dataset sẵn sàng để training ngay
```

#### Option 2: Upload Images
```
1. User vào trang Training
2. Chọn tab "Upload Images"
3. Chọn nhiều ảnh từ thiết bị (jpg, png)
4. Upload và tự động tạo labels rỗng
5. Hiển thị dataset info (tên, số ảnh, has_labels: false)
6. Dataset sẵn sàng để training (với labels rỗng)
```

### 11.2 Training Configuration
```json
{
  "job_name": "license_plate_v3",
  "dataset_id": 1,
  "model_type": "yolov8n",
  "epochs": 50,
  "batch_size": 8,
  "train_val_split": 0.8,
  "learning_rate": 0.01,
  "image_size": 640
}
```

### 11.3 Approval Workflow
```
1. Training completed → Status: 'completed'
2. Admin vào Model Management
3. Xem training results:
   - mAP50, mAP95 scores
   - Training/validation loss curves
   - Sample predictions
4. Click Approve/Reject:
   - Approve → is_approved = TRUE, có thể dùng predict
   - Reject → is_approved = FALSE, ghi approval_notes
```

## 12. TÓM TẮT THAY ĐỔI

### 12.1 Files cần tạo mới
- `app.py` - Flask entry point
- `models/dataset.py` - Dataset entity cho uploaded datasets
- `utils/dataset_utils.py` - Xử lý dataset upload và validation
- `utils/training_utils.py` - Logic training từ main.py
- `controllers/dataset_controller.py` - Dataset management
- `controllers/training_controller.py` - Training management
- `routes/dataset_routes.py` - Dataset API endpoints
- `routes/training_routes.py` - Training API endpoints
- `Dockerfile` - Container configuration
- `uploads/` folder - Lưu uploaded datasets

### 12.2 Files cần cập nhật
- `database/trainregion-init.sql` - Thêm bảng datasets và approval fields
- `models/model.py` - Thêm approval fields và dataset_id
- `models/training_job.py` - Thêm training config fields và dataset_id
- `controllers/model_controller.py` - Thêm approval logic
- `controllers/predict_controller.py` - Chỉ dùng approved models
- `docker-compose.yml` - Thêm volumes cho uploads
- Frontend AI Predict - Thêm dataset upload và training interface

### 12.3 Workflow hoàn chỉnh
1. **Dataset Upload**: Upload ZIP file từ thiết bị
2. **Dataset Validation**: Kiểm tra cấu trúc và lưu vào database
3. **Training Config**: Chọn dataset và cấu hình training parameters
4. **Start Training**: Sử dụng dataset đã upload để training
5. **Monitor Progress**: Polling API để xem progress
6. **Review Results**: Xem metrics và sample predictions
7. **Approve Model**: Admin approve/reject model
8. **Use Model**: Chỉ approved models có thể dùng predict

### 12.4 Lợi ích
- ✅ Sử dụng tối đa code và cấu trúc hiện có
- ✅ Dataset upload linh hoạt từ thiết bị
- ✅ Workflow approval đảm bảo chất lượng model
- ✅ Tích hợp hoàn toàn với hệ thống hiện tại (app-network)
- ✅ Docker deployment đã sẵn sàng
- ✅ MVC structure chuẩn như vehicle-service
- ✅ Mỗi training job tạo dataset riêng biệt

**Kế hoạch này sẽ chuyển đổi trainregion-service thành một microservice MVC hoàn chỉnh, sử dụng tối đa tài nguyên hiện có và tích hợp hoàn toàn với hệ thống hethongbienso.**
