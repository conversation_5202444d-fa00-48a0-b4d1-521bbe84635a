<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mock Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Mock Data</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Login</h3>
                <button class="btn btn-primary" onclick="testLogin()">Test Admin Login</button>
                <button class="btn btn-secondary" onclick="testPoliceLogin()">Test Police Login</button>
                <div id="login-result" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Vehicle Data</h3>
                <button class="btn btn-success" onclick="testVehicles()">Load Vehicles</button>
                <div id="vehicle-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Vehicle List</h3>
                <div id="vehicle-list"></div>
            </div>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script>
        async function testLogin() {
            try {
                const result = await API.auth.login("admin", "admin123");
                document.getElementById("login-result").innerHTML = `
                    <div class="alert alert-success">
                        <strong>Login Success!</strong><br>
                        User: ${result.user.fullName}<br>
                        Role: ${result.user.role}<br>
                        Token: ${result.token}
                    </div>
                `;
                
                // Save to localStorage
                localStorage.setItem("user", JSON.stringify(result.user));
                localStorage.setItem("token", result.token);
            } catch (error) {
                document.getElementById("login-result").innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Login Failed!</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testPoliceLogin() {
            try {
                const result = await API.auth.login("police", "police123");
                document.getElementById("login-result").innerHTML = `
                    <div class="alert alert-success">
                        <strong>Login Success!</strong><br>
                        User: ${result.user.fullName}<br>
                        Role: ${result.user.role}<br>
                        Token: ${result.token}
                    </div>
                `;
                
                // Save to localStorage
                localStorage.setItem("user", JSON.stringify(result.user));
                localStorage.setItem("token", result.token);
            } catch (error) {
                document.getElementById("login-result").innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Login Failed!</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testVehicles() {
            try {
                const vehicles = await API.vehicle.getAllVehicles();
                document.getElementById("vehicle-result").innerHTML = `
                    <div class="alert alert-success">
                        <strong>Vehicles Loaded!</strong><br>
                        Count: ${vehicles.length} vehicles
                    </div>
                `;
                
                // Display vehicles
                let html = '<div class="row">';
                vehicles.forEach(vehicle => {
                    let badgeClass = "bg-success";
                    let statusText = "Hoạt động";
                    
                    switch (vehicle.status) {
                        case "expired":
                            badgeClass = "bg-danger";
                            statusText = "Hết hạn";
                            break;
                        case "suspended":
                            badgeClass = "bg-warning";
                            statusText = "Tạm dừng";
                            break;
                    }
                    
                    html += `
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <h6>${vehicle.licensePlate} 
                                        <span class="badge ${badgeClass}">${statusText}</span>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        <strong>${vehicle.brand} ${vehicle.model}</strong><br>
                                        Chủ xe: ${vehicle.ownerName}<br>
                                        Loại xe: ${vehicle.vehicleType}<br>
                                        Màu: ${vehicle.color}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                
                document.getElementById("vehicle-list").innerHTML = html;
            } catch (error) {
                document.getElementById("vehicle-result").innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Failed to load vehicles!</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        // Auto test on page load
        window.onload = function() {
            console.log("Testing mock data...");
            testLogin();
            setTimeout(() => {
                testVehicles();
            }, 1000);
        };
    </script>
</body>
</html>
