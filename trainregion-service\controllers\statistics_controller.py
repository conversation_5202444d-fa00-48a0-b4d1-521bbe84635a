import logging
from flask import jsonify
from config.config import SessionLocal
from models.training_statistics import TrainingStatistics, TrainingOutcome, TrainingStatus
from sqlalchemy import func, desc

logger = logging.getLogger(__name__)

class StatisticsController:
    def __init__(self):
        pass

    def get_training_statistics(self):
        """
        <PERSON><PERSON>y tổng quan thống kê training
        """
        try:
            db = SessionLocal()
            try:
                # Basic counts
                total_trainings = db.query(TrainingStatistics).count()
                completed_trainings = db.query(TrainingStatistics).filter(
                    TrainingStatistics.status == TrainingStatus.COMPLETED
                ).count()
                
                saved_models = db.query(TrainingStatistics).filter(
                    TrainingStatistics.outcome == TrainingOutcome.SAVED
                ).count()
                
                discarded_models = db.query(TrainingStatistics).filter(
                    TrainingStatistics.outcome == TrainingOutcome.DISCARDED
                ).count()

                # Success rate
                success_rate = (saved_models / completed_trainings * 100) if completed_trainings > 0 else 0

                # Average training time
                avg_training_time = db.query(func.avg(TrainingStatistics.training_time)).filter(
                    TrainingStatistics.status == TrainingStatus.COMPLETED
                ).scalar() or 0

                # Best performance
                best_map50 = db.query(func.max(TrainingStatistics.best_map50)).scalar() or 0
                best_map95 = db.query(func.max(TrainingStatistics.best_map95)).scalar() or 0

                # Model type distribution
                model_type_stats = db.query(
                    TrainingStatistics.model_type,
                    func.count(TrainingStatistics.id).label('count')
                ).group_by(TrainingStatistics.model_type).all()

                # Recent trainings
                recent_trainings = db.query(TrainingStatistics).order_by(
                    desc(TrainingStatistics.started_at)
                ).limit(10).all()

                return jsonify({
                    'success': True,
                    'statistics': {
                        'overview': {
                            'total_trainings': total_trainings,
                            'completed_trainings': completed_trainings,
                            'saved_models': saved_models,
                            'discarded_models': discarded_models,
                            'success_rate': round(success_rate, 1),
                            'avg_training_time_minutes': round(avg_training_time / 60, 1) if avg_training_time else 0
                        },
                        'performance': {
                            'best_map50': round(best_map50, 3),
                            'best_map95': round(best_map95, 3)
                        },
                        'model_types': [
                            {
                                'model_type': stat.model_type,
                                'count': stat.count
                            } for stat in model_type_stats
                        ],
                        'recent_trainings': [stat.to_dict() for stat in recent_trainings]
                    }
                }), 200

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Get training statistics error: {e}")
            return jsonify({'error': str(e)}), 500

    def get_detailed_statistics(self):
        """
        Lấy thống kê chi tiết với filters
        """
        try:
            db = SessionLocal()
            try:
                # All training records
                all_trainings = db.query(TrainingStatistics).order_by(
                    desc(TrainingStatistics.started_at)
                ).all()

                # Performance by model type
                performance_by_model = db.query(
                    TrainingStatistics.model_type,
                    func.avg(TrainingStatistics.best_map50).label('avg_map50'),
                    func.avg(TrainingStatistics.best_map95).label('avg_map95'),
                    func.avg(TrainingStatistics.training_time).label('avg_time'),
                    func.count(TrainingStatistics.id).label('count')
                ).filter(
                    TrainingStatistics.status == TrainingStatus.COMPLETED
                ).group_by(TrainingStatistics.model_type).all()

                # Training trends by month
                monthly_stats = db.query(
                    func.date_format(TrainingStatistics.started_at, '%Y-%m').label('month'),
                    func.count(TrainingStatistics.id).label('total'),
                    func.sum(func.case([(TrainingStatistics.outcome == TrainingOutcome.SAVED, 1)], else_=0)).label('saved'),
                    func.sum(func.case([(TrainingStatistics.outcome == TrainingOutcome.DISCARDED, 1)], else_=0)).label('discarded')
                ).group_by(func.date_format(TrainingStatistics.started_at, '%Y-%m')).order_by('month').all()

                return jsonify({
                    'success': True,
                    'detailed_statistics': {
                        'all_trainings': [stat.to_dict() for stat in all_trainings],
                        'performance_by_model': [
                            {
                                'model_type': stat.model_type,
                                'avg_map50': round(float(stat.avg_map50 or 0), 3),
                                'avg_map95': round(float(stat.avg_map95 or 0), 3),
                                'avg_training_time_minutes': round(float(stat.avg_time or 0) / 60, 1),
                                'count': stat.count
                            } for stat in performance_by_model
                        ],
                        'monthly_trends': [
                            {
                                'month': stat.month,
                                'total': stat.total,
                                'saved': stat.saved or 0,
                                'discarded': stat.discarded or 0,
                                'success_rate': round((stat.saved or 0) / stat.total * 100, 1) if stat.total > 0 else 0
                            } for stat in monthly_stats
                        ]
                    }
                }), 200

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Get detailed statistics error: {e}")
            return jsonify({'error': str(e)}), 500

    def get_statistics_summary(self):
        """
        Lấy summary ngắn gọn cho dashboard
        """
        try:
            db = SessionLocal()
            try:
                # Quick stats
                total = db.query(TrainingStatistics).count()
                saved = db.query(TrainingStatistics).filter(
                    TrainingStatistics.outcome == TrainingOutcome.SAVED
                ).count()
                
                success_rate = (saved / total * 100) if total > 0 else 0

                return jsonify({
                    'success': True,
                    'summary': {
                        'total_trainings': total,
                        'saved_models': saved,
                        'success_rate': round(success_rate, 1)
                    }
                }), 200

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Get statistics summary error: {e}")
            return jsonify({'error': str(e)}), 500
