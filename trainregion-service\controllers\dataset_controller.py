import os
import zipfile
from flask import request, jsonify
from config.config import SessionLocal
from models.dataset import Dataset
from utils.auto_labeling import <PERSON>Labeler
import logging

logger = logging.getLogger(__name__)

class DatasetController:
    def __init__(self):
        self.auto_labeler = AutoLabeler()
    
    def upload_images_smart(self):
        """
        Upload nhiều ảnh với auto-labeling
        """
        try:
            files = request.files.getlist('images')
            method = request.form.get('method', 'auto')
            dataset_name = request.form.get('name', 'Untitled Dataset')
            description = request.form.get('description', '')
            confidence_threshold = float(request.form.get('confidence_threshold', 0.3))
            
            if not files:
                return jsonify({'error': 'No images provided'}), 400
            
            # Validate image files
            valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
            valid_files = []
            total_size = 0
            
            for file in files:
                if file.filename:
                    ext = os.path.splitext(file.filename.lower())[1]
                    if ext in valid_extensions:
                        valid_files.append(file)
                        # Estimate file size (rough)
                        file.seek(0, 2)  # Seek to end
                        total_size += file.tell()
                        file.seek(0)  # Reset to beginning
            
            if not valid_files:
                return jsonify({'error': 'No valid image files found'}), 400
            
            db = SessionLocal()
            try:
                # Tạo dataset record
                dataset = Dataset(
                    name=dataset_name,
                    description=description,
                    upload_path='',  # Sẽ update sau
                    image_count=len(valid_files),
                    file_size=total_size,
                    upload_type='images',
                    has_labels=False,  # Sẽ update sau
                    labeling_method=method,
                    status='processing'
                )
                
                db.add(dataset)
                db.commit()
                db.refresh(dataset)
                
                # Update upload path
                dataset.upload_path = f"uploads/dataset_{dataset.id}"
                
                # Tạo thư mục uploads nếu chưa có
                os.makedirs("uploads", exist_ok=True)
                
                # Process images với auto-labeling
                result = self.auto_labeler.process_images(
                    valid_files, 
                    dataset.id, 
                    method, 
                    confidence_threshold
                )
                
                # Update dataset với kết quả
                dataset.has_labels = result['labeled_images'] > 0
                dataset.labeling_accuracy = result['accuracy_estimate']
                dataset.status = 'ready'
                
                db.commit()
                
                return jsonify({
                    'success': True,
                    'dataset': dataset.to_dict(),
                    'labeling_result': result
                }), 201
                
            except Exception as e:
                db.rollback()
                logger.error(f"Database error: {e}")
                return jsonify({'error': 'Database error occurred'}), 500
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Upload error: {e}")
            return jsonify({'error': str(e)}), 500
    
    def upload_zip_dataset(self):
        """
        Upload ZIP file chứa dataset hoàn chỉnh
        """
        try:
            if 'dataset' not in request.files:
                return jsonify({'error': 'No dataset file provided'}), 400
            
            file = request.files['dataset']
            dataset_name = request.form.get('name', 'Untitled Dataset')
            description = request.form.get('description', '')
            
            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400
            
            if not file.filename.lower().endswith('.zip'):
                return jsonify({'error': 'File must be a ZIP archive'}), 400
            
            db = SessionLocal()
            try:
                # Tạo dataset record
                dataset = Dataset(
                    name=dataset_name,
                    description=description,
                    upload_path='',
                    upload_type='zip',
                    has_labels=False,
                    labeling_method='manual',
                    status='processing'
                )
                
                db.add(dataset)
                db.commit()
                db.refresh(dataset)
                
                # Lưu và extract ZIP file
                dataset_path = f"uploads/dataset_{dataset.id}"
                zip_path = f"{dataset_path}.zip"
                
                os.makedirs("uploads", exist_ok=True)
                file.save(zip_path)
                
                # Extract và validate
                validation_result = self._extract_and_validate_zip(zip_path, dataset_path)
                
                if not validation_result['valid']:
                    dataset.status = 'error'
                    db.commit()
                    return jsonify({'error': validation_result['message']}), 400
                
                # Update dataset
                dataset.upload_path = dataset_path
                dataset.image_count = validation_result['image_count']
                dataset.file_size = os.path.getsize(zip_path)
                dataset.has_labels = validation_result['has_labels']
                dataset.status = 'ready'
                
                db.commit()
                
                # Xóa ZIP file sau khi extract
                os.remove(zip_path)
                
                return jsonify({
                    'success': True,
                    'dataset': dataset.to_dict(),
                    'validation_result': validation_result
                }), 201
                
            except Exception as e:
                db.rollback()
                logger.error(f"Database error: {e}")
                return jsonify({'error': 'Database error occurred'}), 500
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"ZIP upload error: {e}")
            return jsonify({'error': str(e)}), 500
    
    def get_all_datasets(self):
        """
        Lấy danh sách tất cả datasets
        """
        try:
            db = SessionLocal()
            try:
                datasets = db.query(Dataset).order_by(Dataset.created_at.desc()).all()
                
                result = []
                for dataset in datasets:
                    dataset_dict = dataset.to_dict()
                    # Thêm thông tin về file existence
                    dataset_dict['path_exists'] = os.path.exists(dataset.upload_path) if dataset.upload_path else False
                    result.append(dataset_dict)
                
                return jsonify({
                    'success': True,
                    'datasets': result,
                    'total': len(result)
                }), 200
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Get datasets error: {e}")
            return jsonify({'error': str(e)}), 500
    
    def get_dataset_by_id(self, dataset_id):
        """
        Lấy thông tin dataset theo ID
        """
        try:
            db = SessionLocal()
            try:
                dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
                
                if not dataset:
                    return jsonify({'error': 'Dataset not found'}), 404
                
                dataset_dict = dataset.to_dict()
                dataset_dict['path_exists'] = os.path.exists(dataset.upload_path) if dataset.upload_path else False
                
                # Thêm thông tin chi tiết về files
                if dataset.upload_path and os.path.exists(dataset.upload_path):
                    images_path = os.path.join(dataset.upload_path, 'images')
                    labels_path = os.path.join(dataset.upload_path, 'labels')
                    
                    if os.path.exists(images_path):
                        images = [f for f in os.listdir(images_path) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                        dataset_dict['actual_image_count'] = len(images)
                        dataset_dict['sample_images'] = images[:5]  # First 5 images
                    
                    if os.path.exists(labels_path):
                        labels = [f for f in os.listdir(labels_path) if f.endswith('.txt')]
                        dataset_dict['label_count'] = len(labels)
                
                return jsonify({
                    'success': True,
                    'dataset': dataset_dict
                }), 200
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Get dataset error: {e}")
            return jsonify({'error': str(e)}), 500
    
    def delete_dataset(self, dataset_id):
        """
        Xóa dataset
        """
        try:
            db = SessionLocal()
            try:
                dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
                
                if not dataset:
                    return jsonify({'error': 'Dataset not found'}), 404
                
                # Xóa files
                if dataset.upload_path and os.path.exists(dataset.upload_path):
                    import shutil
                    shutil.rmtree(dataset.upload_path)
                
                # Xóa record
                db.delete(dataset)
                db.commit()
                
                return jsonify({
                    'success': True,
                    'message': 'Dataset deleted successfully'
                }), 200
                
            except Exception as e:
                db.rollback()
                logger.error(f"Delete dataset error: {e}")
                return jsonify({'error': 'Failed to delete dataset'}), 500
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Delete dataset error: {e}")
            return jsonify({'error': str(e)}), 500
    
    def _extract_and_validate_zip(self, zip_path, extract_path):
        """
        Extract và validate ZIP dataset
        """
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
            
            # Validate structure
            required_folders = ['images']
            optional_folders = ['labels']
            
            images_path = os.path.join(extract_path, 'images')
            labels_path = os.path.join(extract_path, 'labels')
            classes_path = os.path.join(extract_path, 'classes.txt')
            
            if not os.path.exists(images_path):
                return {'valid': False, 'message': 'Missing images folder'}
            
            # Count images
            images = [f for f in os.listdir(images_path) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            if len(images) == 0:
                return {'valid': False, 'message': 'No valid images found'}
            
            # Check labels
            has_labels = os.path.exists(labels_path)
            if not has_labels:
                # Tạo labels folder và files rỗng
                os.makedirs(labels_path, exist_ok=True)
                for img in images:
                    label_name = os.path.splitext(img)[0] + '.txt'
                    with open(os.path.join(labels_path, label_name), 'w') as f:
                        pass
            
            # Tạo classes.txt nếu chưa có
            if not os.path.exists(classes_path):
                with open(classes_path, 'w') as f:
                    f.write('license_plate\n')
            
            return {
                'valid': True,
                'image_count': len(images),
                'has_labels': has_labels,
                'message': 'Dataset validated successfully'
            }
            
        except Exception as e:
            return {'valid': False, 'message': f'Validation error: {str(e)}'}
    
    def get_auto_labeler_info(self):
        """
        Lấy thông tin về auto-labeler
        """
        try:
            info = self.auto_labeler.get_model_info()
            return jsonify({
                'success': True,
                'auto_labeler': info
            }), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500
