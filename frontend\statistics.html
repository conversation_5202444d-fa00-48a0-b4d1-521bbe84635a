<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Statistics - AI Training System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stats-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stats-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stats-card.info {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .model-type-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        
        .performance-bar {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-chart-line me-2"></i>AI Training Statistics
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="ai-training.html">
                            <i class="fas fa-robot me-1"></i>AI Training
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="statistics.html">
                            <i class="fas fa-chart-bar me-1"></i>Statistics
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Loading State -->
        <div id="loadingState" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading statistics data...</p>
        </div>

        <!-- Error State -->
        <div id="errorState" class="alert alert-danger d-none" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Error!</strong> <span id="errorMessage"></span>
            <button class="btn btn-sm btn-outline-danger ms-3" onclick="refreshData()">
                <i class="fas fa-redo me-1"></i>Retry
            </button>
        </div>

        <!-- Statistics Content -->
        <div id="statisticsContent" class="d-none">
            <!-- Overview Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="metric-value" id="totalModels">0</div>
                        <div class="metric-label">
                            <i class="fas fa-cube me-1"></i>Total Models
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card success">
                        <div class="metric-value" id="completedModels">0</div>
                        <div class="metric-label">
                            <i class="fas fa-check-circle me-1"></i>Completed
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card info">
                        <div class="metric-value" id="successRate">0%</div>
                        <div class="metric-label">
                            <i class="fas fa-percentage me-1"></i>Success Rate
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card warning">
                        <div class="metric-value" id="bestMap50">0.000</div>
                        <div class="metric-label">
                            <i class="fas fa-trophy me-1"></i>Best mAP50
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-pie me-2"></i>Model Types Distribution
                        </h5>
                        <canvas id="modelTypesChart" width="400" height="300"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-line me-2"></i>Training Trends
                        </h5>
                        <canvas id="trendsChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Model Performance Comparison -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="chart-container">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-bar me-2"></i>Model Performance Comparison
                        </h5>
                        <div id="modelPerformanceContainer">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Models Table -->
            <div class="row">
                <div class="col-12">
                    <div class="table-container">
                        <h5 class="mb-3">
                            <i class="fas fa-medal me-2"></i>Top Performing Models
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Rank</th>
                                        <th>Model Name</th>
                                        <th>Type</th>
                                        <th>mAP50</th>
                                        <th>mAP95</th>
                                        <th>Training Time</th>
                                        <th>Dataset Size</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="topModelsTable">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Statistics JavaScript -->
    <script src="js/statistics.js"></script>
</body>
</html>
