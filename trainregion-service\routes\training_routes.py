from flask import Blueprint
from controllers.training_controller import TrainingController

# Tạo Blueprint cho training routes
training_bp = Blueprint('training', __name__)

# Initialize controller
training_controller = TrainingController()

@training_bp.route('/start', methods=['POST'])
def start_training():
    """
    Bắt đầu training job mới

    JSON body:
    {
        "name": "License Plate Model v2",
        "dataset_id": 1,
        "model_type": "yolov8n",
        "epochs": 100,
        "batch_size": 16,
        "learning_rate": 0.01,
        "image_size": 640
    }
    """
    return training_controller.start_training()

@training_bp.route('/jobs', methods=['GET'])
def get_training_jobs():
    """
    <PERSON><PERSON><PERSON> da<PERSON> s<PERSON>ch tất cả training jobs
    """
    return training_controller.get_training_jobs()

@training_bp.route('/jobs/<int:job_id>', methods=['GET'])
def get_training_job(job_id):
    """
    <PERSON><PERSON><PERSON> thông tin chi tiết training job theo ID
    """
    return training_controller.get_training_job_by_id(job_id)

@training_bp.route('/jobs/<int:job_id>/stop', methods=['POST'])
def stop_training_job(job_id):
    """
    Dừng training job đang chạy
    """
    try:
        from flask import current_app
        current_app.logger.info(f"Stop route called with job_id: {job_id}, type: {type(job_id)}")
        return training_controller.stop_training_job(job_id)
    except Exception as e:
        current_app.logger.error(f"Stop route error: {e}")
        from flask import jsonify
        return jsonify({'error': f'Route error: {str(e)}'}), 500

@training_bp.route('/jobs/<int:job_id>/save-model', methods=['POST', 'OPTIONS'])
def save_model_manual(job_id):
    """Save model from training job"""
    from flask import request, jsonify

    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST')
        return response

    # Simple test response
    return jsonify({
        'success': True,
        'message': f'Save model endpoint working for job {job_id}',
        'job_id': job_id
    }), 200

@training_bp.route('/jobs/<int:job_id>/debug-stop', methods=['POST'])
def debug_stop_training_job(job_id):
    """
    Debug version of stop training job
    """
    from flask import jsonify
    try:
        return jsonify({
            'success': True,
            'message': f'Debug stop called for job {job_id}',
            'job_id': job_id,
            'job_id_type': str(type(job_id))
        }), 200
    except Exception as e:
        return jsonify({'error': f'Debug stop error: {str(e)}'}), 500

@training_bp.route('/jobs/<int:job_id>/test-progress', methods=['POST'])
def test_progress_update(job_id):
    """
    Test progress update cho debugging
    """
    return training_controller.test_progress_update(job_id)

@training_bp.route('/stats', methods=['GET'])
def get_training_stats():
    """
    Lấy thống kê về training jobs
    """
    return training_controller.get_training_stats()

@training_bp.route('/test-save', methods=['POST', 'OPTIONS'])
def test_save_endpoint():
    """Test save endpoint"""
    from flask import jsonify
    return jsonify({'success': True, 'message': 'Test endpoint working'}), 200
