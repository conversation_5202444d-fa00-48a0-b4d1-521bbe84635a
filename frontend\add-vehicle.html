<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Thêm/S<PERSON>a xe - <PERSON>ệ thống quản lý <PERSON> số xe</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
      .sidebar {
        min-height: 100vh;
        background-color: #343a40;
      }
      .sidebar .nav-link {
        color: #fff;
        padding: 15px 20px;
      }
      .sidebar .nav-link:hover {
        background-color: #495057;
        color: #fff;
      }
      .sidebar .nav-link.active {
        background-color: #007bff;
        color: #fff;
      }
      .main-content {
        padding: 20px;
      }
      .form-container {
        background-color: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-2 d-md-block sidebar">
          <div class="position-sticky pt-3">
            <h5 class="text-white text-center mb-4">Hệ thống quản lý</h5>
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link" href="dashboard.html">
                  <i class="fas fa-tachometer-alt me-2"></i>Tổng quan
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link active" href="add-vehicle.html">
                  <i class="fas fa-plus me-2"></i>Thêm xe mới
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="ai-training.html">
                  <i class="fas fa-brain me-2"></i>Huấn luyện AI
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="ai-predict.html">
                  <i class="fas fa-search me-2"></i>Dự đoán AI
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="model-stats.html">
                  <i class="fas fa-chart-bar me-2"></i>Thống kê mô hình
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" onclick="logout()">
                  <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                </a>
              </li>
            </ul>
            <div class="mt-4 px-3">
              <div class="text-white-50 small">
                <div id="user-info-sidebar"></div>
              </div>
            </div>
          </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-10 ms-sm-auto main-content">
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" id="form-title">Thêm xe mới</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <button type="button" class="btn btn-secondary" onclick="window.location.href='dashboard.html'">
                <i class="fas fa-arrow-left me-1"></i>Quay lại
              </button>
            </div>
          </div>

          <div class="form-container">
            <div
              class="alert alert-danger"
              id="form-error"
              style="display: none"
            ></div>

        <form id="vehicle-form">
          <input type="hidden" id="vehicle-id" />

          <div class="mb-3">
            <label for="license-plate" class="form-label">Biển số xe *</label>
            <input
              type="text"
              class="form-control"
              id="license-plate"
              required
            />
          </div>

          <div class="mb-3">
            <label for="owner-name" class="form-label">Tên chủ sở hữu *</label>
            <input type="text" class="form-control" id="owner-name" required />
          </div>

          <div class="mb-3">
            <label for="owner-id" class="form-label">CMND/CCCD *</label>
            <input type="text" class="form-control" id="owner-id" required />
          </div>

          <div class="mb-3">
            <label for="vehicle-type" class="form-label">Loại xe *</label>
            <input
              type="text"
              class="form-control"
              id="vehicle-type"
              required
            />
          </div>

          <div class="mb-3">
            <label for="brand" class="form-label">Hãng xe *</label>
            <input type="text" class="form-control" id="brand" required />
          </div>

          <div class="mb-3">
            <label for="model" class="form-label">Mẫu xe *</label>
            <input type="text" class="form-control" id="model" required />
          </div>

          <div class="mb-3">
            <label for="color" class="form-label">Màu sắc *</label>
            <input type="text" class="form-control" id="color" required />
          </div>

          <div class="mb-3">
            <label for="registration-date" class="form-label"
              >Ngày đăng ký *</label
            >
            <input
              type="date"
              class="form-control"
              id="registration-date"
              required
            />
          </div>

          <div class="mb-3">
            <label for="expiry-date" class="form-label">Ngày hết hạn *</label>
            <input type="date" class="form-control" id="expiry-date" required />
          </div>

          <div class="mb-3">
            <label for="status" class="form-label">Trạng thái *</label>
            <select class="form-select" id="status" required>
              <option value="active">Hoạt động</option>
              <option value="expired">Hết hạn</option>
              <option value="suspended">Tạm dừng</option>
            </select>
          </div>

          <div class="d-flex gap-2">
            <button type="submit" class="btn btn-primary" id="save-btn">
              <i class="fas fa-save me-1"></i>Lưu thông tin
            </button>
            <button type="button" class="btn btn-secondary" id="cancel-btn">
              <i class="fas fa-times me-1"></i>Hủy bỏ
            </button>
          </div>
        </form>
          </div>
        </main>
      </div>
    </div>

    <script src="js/api.js"></script>
    <script>
      // Check if user is logged in and is admin
      const user = JSON.parse(localStorage.getItem("user"));
      const token = localStorage.getItem("token");

      if (!user || !token) {
        window.location.href = "login.html";
      } else if (user.role !== "admin") {
        window.location.href = "dashboard.html";
      }

      // Update UI
      document.getElementById("user-info-sidebar").innerHTML = `
        <strong>${user.fullName}</strong><br>
        <small>${user.role === "admin" ? "Quản trị viên" : "Cảnh sát"}</small>
      `;

      // Logout functionality
      function logout() {
        localStorage.removeItem("user");
        localStorage.removeItem("token");
        window.location.href = "login.html";
      }

      // Check if editing existing vehicle
      const urlParams = new URLSearchParams(window.location.search);
      const vehicleId = urlParams.get("id");
      const nextVehicleId = urlParams.get("nextId");
      let isEditMode = !!vehicleId;

      // Form elements
      const vehicleForm = document.getElementById("vehicle-form");
      const formTitle = document.getElementById("form-title");
      const formError = document.getElementById("form-error");
      const vehicleIdInput = document.getElementById("vehicle-id");
      const licensePlate = document.getElementById("license-plate");
      const ownerName = document.getElementById("owner-name");
      const ownerId = document.getElementById("owner-id");
      const vehicleType = document.getElementById("vehicle-type");
      const brand = document.getElementById("brand");
      const model = document.getElementById("model");
      const color = document.getElementById("color");
      const registrationDate = document.getElementById("registration-date");
      const expiryDate = document.getElementById("expiry-date");
      const status = document.getElementById("status");

      // If editing, populate form with vehicle data
      if (isEditMode) {
        formTitle.textContent = "Sửa thông tin xe";

        // Xác định ID của xe cần hiển thị thông tin
        const displayVehicleId = nextVehicleId || vehicleId;

        // Fetch vehicle data
        API.vehicle
          .getVehicleById(displayVehicleId)
          .then((vehicle) => {
            // Lưu thông tin ban đầu của xe
            originalVehicleData = vehicle;

            // Lưu ID của xe kế tiếp vào input hidden để cập nhật đúng xe
            vehicleIdInput.value = displayVehicleId;

            // Hiển thị thông tin của xe kế tiếp
            licensePlate.value = vehicle.licensePlate;
            ownerName.value = vehicle.ownerName;
            ownerId.value = vehicle.ownerID;
            vehicleType.value = vehicle.vehicleType;
            brand.value = vehicle.brand;
            model.value = vehicle.model;
            color.value = vehicle.color;
            registrationDate.value = vehicle.registrationDate.split("T")[0];
            expiryDate.value = vehicle.expiryDate.split("T")[0];
            status.value = vehicle.status;

            // Thêm thông báo để người dùng biết đang sửa xe nào
            formTitle.textContent = `Sửa thông tin xe (Biển số: ${vehicle.licensePlate})`;
          })
          .catch((error) => {
            formError.textContent =
              "Không tìm thấy thông tin xe: " + error.message;
            formError.style.display = "block";
          });
      }

      // Biến để lưu thông tin xe ban đầu
      let originalVehicleData = null;

      // Form submission
      vehicleForm.addEventListener("submit", async function (e) {
        e.preventDefault();

        // Get form data
        const formData = {
          ownerName: ownerName.value,
          ownerID: ownerId.value,
          vehicleType: vehicleType.value,
          brand: brand.value,
          model: model.value,
          color: color.value,
          registrationDate: registrationDate.value,
          expiryDate: expiryDate.value,
          status: status.value,
        };

        // Chỉ thêm biển số vào formData nếu đây là thêm mới hoặc biển số đã thay đổi
        if (
          !isEditMode ||
          (originalVehicleData &&
            licensePlate.value !== originalVehicleData.licensePlate)
        ) {
          formData.licensePlate = licensePlate.value;
        }

        try {
          if (isEditMode) {
            // Lấy ID của xe cần cập nhật từ input hidden
            const updateVehicleId = vehicleIdInput.value;

            // Update existing vehicle
            await API.vehicle.updateVehicle(updateVehicleId, formData);
          } else {
            // Add new vehicle
            await API.vehicle.createVehicle(formData);
          }

          // Redirect back to dashboard
          window.location.href = "dashboard.html";
        } catch (error) {
          formError.textContent = "Lỗi khi lưu thông tin xe: " + error.message;
          formError.style.display = "block";
        }
      });

      // Cancel button
      document
        .getElementById("cancel-btn")
        .addEventListener("click", function () {
          window.location.href = "dashboard.html";
        });
    </script>
  </body>
</html>

