{"name": "model-statistics-service", "version": "1.0.0", "description": "Model Statistics Service for AI Training System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["statistics", "model", "training", "analytics", "microservice"], "author": "AI Training System", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.0.0", "morgan": "^1.10.0", "express-rate-limit": "^6.10.0", "joi": "^17.9.2", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}