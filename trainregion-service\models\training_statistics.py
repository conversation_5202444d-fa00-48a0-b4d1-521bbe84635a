from sqlalchemy import Column, Integer, String, Float, DateTime, Enum, Text
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import enum

Base = declarative_base()

class TrainingOutcome(enum.Enum):
    SAVED = "saved"
    DISCARDED = "discarded"
    FAILED = "failed"

class TrainingStatus(enum.Enum):
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"

class TrainingStatistics(Base):
    __tablename__ = 'training_statistics'

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Training Job Info
    original_job_id = Column(Integer, nullable=False)  # Reference to original training_job
    job_name = Column(String(255), nullable=False)
    
    # Dataset Info
    dataset_id = Column(Integer, nullable=True)
    dataset_name = Column(String(255), nullable=True)
    dataset_size = Column(Integer, default=0)
    
    # Training Configuration
    model_type = Column(String(50), nullable=False)
    epochs = Column(Integer, nullable=False)
    batch_size = Column(Integer, nullable=False)
    learning_rate = Column(Float, nullable=False)
    image_size = Column(Integer, default=640)
    
    # Training Results
    training_time = Column(Float, default=0.0)  # in seconds
    final_epoch = Column(Integer, default=0)
    final_map50 = Column(Float, default=0.0)
    final_map95 = Column(Float, default=0.0)
    best_map50 = Column(Float, default=0.0)
    best_map95 = Column(Float, default=0.0)
    
    # Status & Outcome
    status = Column(Enum(TrainingStatus), default=TrainingStatus.RUNNING)
    outcome = Column(Enum(TrainingOutcome), nullable=True)  # Set when user decides
    
    # Model Info (if saved)
    saved_model_id = Column(Integer, nullable=True)  # Reference to models table
    saved_model_name = Column(String(255), nullable=True)
    
    # Timestamps
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    decided_at = Column(DateTime, nullable=True)  # When user saved/discarded
    
    # Additional Info
    notes = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)  # If failed
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<TrainingStatistics(id={self.id}, job_name='{self.job_name}', model_type='{self.model_type}', outcome='{self.outcome}')>"

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'original_job_id': self.original_job_id,
            'job_name': self.job_name,
            'dataset_id': self.dataset_id,
            'dataset_name': self.dataset_name,
            'dataset_size': self.dataset_size,
            'model_type': self.model_type,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate,
            'image_size': self.image_size,
            'training_time': self.training_time,
            'final_epoch': self.final_epoch,
            'final_map50': self.final_map50,
            'final_map95': self.final_map95,
            'best_map50': self.best_map50,
            'best_map95': self.best_map95,
            'status': self.status.value if self.status else None,
            'outcome': self.outcome.value if self.outcome else None,
            'saved_model_id': self.saved_model_id,
            'saved_model_name': self.saved_model_name,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'decided_at': self.decided_at.isoformat() if self.decided_at else None,
            'notes': self.notes,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
