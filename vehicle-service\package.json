{"name": "vehicle-service", "version": "1.0.0", "description": "Vehicle registration service for license plate violation detection system", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.3.5", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mysql2": "^3.2.0", "sequelize": "^6.30.0"}, "devDependencies": {"nodemon": "^2.0.22"}}